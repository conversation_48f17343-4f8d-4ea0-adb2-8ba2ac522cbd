{"name": "lifepartner-mother", "version": "1.0.3", "description": "本地生活代运营商后台和商家后台", "main": "index.js", "keywords": ["monorepo"], "scripts": {"prebootstrap": "[ -d ./.hooks ] && (git config --add core.hooksPath ./.hooks) || ([ -d ./.git ] && formula githooks || echo 'Please run git init')", "bootstrap": "yarn --ignore-engines", "dev:seller_all": "LOGAN_PROXY_KEY=life-nandong-sit lerna run --parallel --scope='life{seller,catering,item,order,note,settlepc}' dev", "start:seller_all": "LOGAN_PROXY_KEY=life-nandong lerna run --parallel --scope='life{seller,catering,item,order,note}' dev", "dev:seller": "LOGAN_PROXY_KEY=life-jinyuan-sit formula dev -d packages/lifeseller -p 4000", "dev:partner": "LOGAN_PROXY_KEY=life-jinyuan-sit APP_PLATFORM=partner formula dev -d packages/lifepartner -p 3000", "dev:provider": "LOGAN_PROXY_KEY=partner-weidong APP_PLATFORM=partner lerna run --parallel --scope='life{partner,provider,catering,note}' dev", "start:provider": "LOGAN_PROXY_KEY=life-jinyuan lerna run --parallel --scope='life{partner,provider}' dev", "dev:note": "LOGAN_PROXY_KEY=life-nandong-sit lerna run --parallel --scope='life{seller,note}' dev", "dev:item": "LOGAN_PROXY_KEY=life-nandong-sit lerna run --parallel --scope='life{seller,item}' dev", "dev:item2": "lerna run --parallel --scope='lifeitem' dev", "dev:order": "LOGAN_PROXY_KEY=life-aoxue-sit lerna run --parallel --scope='life{seller,order}' dev", "dev:catering": "LOGAN_PROXY_KEY=life-weidong-beta lerna run --parallel --scope='life{seller,catering}' dev", "dev:activity": "LOGAN_PROXY_KEY=life-nandong-sit lerna run --parallel --scope='life{seller,activity}' dev", "dev:camp-admin": "LOGAN_PROXY_KEY=hera-zidi-sit yarn workspace camp-admin run dev", "start:catering": "LOGAN_PROXY_KEY=ark-jinyuan lerna run --parallel --scope='life{seller,catering}' dev", "push:arkappcatering": "git push origin HEAD:ark-app-catering/ci", "dev:h5": "LOGAN_PROXY_KEY=zhangyang7-life-sit yarn workspace lifesellerh5 run dev", "dev:zhaoshangh5": "LOGAN_PROXY_KEY=life-jinyuan-sit yarn workspace lifezhaoshangh5 run dev", "dev:zhaoshangpc": "LOGAN_PROXY_KEY=life-shanjian-sit yarn workspace lifezhaoshangpc run dev", "dev:settlepc": "LOGAN_PROXY_KEY=life-shanjian-sit yarn workspace lifesettlepc run dev", "push:catering": "git push origin HEAD:${BRANCH:-lifecatering/ci}", "push:seller": "git push origin HEAD:${BRANCH:-lifeseller/ci}", "push:partner": "git push origin HEAD:${BRANCH:-lifepartner/ci}", "push:provider": "git push origin HEAD:${BRANCH:-lifeprovider/ci}", "push:order": "git push origin HEAD:${BRANCH:-lifeorder/ci}", "push:note": "git push origin HEAD:${BRANCH:-lifenote/ci}", "push:item": "git push origin HEAD:${BRANCH:-lifeitem/ci}", "push:activity": "git push origin HEAD:${BRANCH:-lifeactivity/ci}", "push:zhaoshangh5": "git push origin HEAD:${BRANCH:-lifezhaoshangh5/ci}", "push:zhaoshangpc": "git push origin HEAD:${BRANCH:-lifezhaoshangpc/ci}", "push:camp-admin": "git push origin HEAD:${BRANCH:-camp-admin/ci}", "push:h5": "git push origin HEAD:${BRANCH:-lifesellerh5/ci}", "push:all": "git push origin HEAD:${BRANCH:-lifecatering/ci} HEAD:${BRANCH:-lifeseller/ci} HEAD:${BRANCH:-lifepartner/ci} HEAD:${BRANCH:-lifeprovider/ci} HEAD:${BRANCH:-lifeorder/ci} HEAD:${BRANCH:-lifenote/ci} HEAD:${BRANCH:-lifeitem/ci} HEAD:${BRANCH:-lifeactivity/ci} HEAD:${BRANCH:-lifezhaoshangh5/ci} HEAD:${BRANCH:-lifezhaoshangpc/ci} HEAD:${BRANCH:-camp-admin/ci} HEAD:${BRANCH:-lifesellerh5/ci}", "lint:fix": "formula lint -p --fix", "devtools": "devtool start ./devtools.config.js"}, "repository": {"type": "git", "url": "*******************************:fe/lifepartner.git"}, "author": "luzhongkuan <<EMAIL>>", "devDependencies": {"@types/node": "^20.5.9", "happypack": "^5.0.1", "lerna": "3.22.1"}, "resolutions": {"@xhs/delight-formily/@xhs/delight": "1.2.15", "@xhs/delight-material-ads-note-detail/@xhs/delight": "1.2.15"}, "dependencies": {"@babel/plugin-proposal-decorators": "^7.24.6", "@formily/core": "2.2.7", "@formily/reactive": "2.2.7", "@formily/reactive-vue": "2.2.7", "@formily/shared": "2.2.7", "@formily/vue": "2.2.7", "@vant/lazyload": "^1.2.0", "@xhs/apm-insight": "^1.1.8", "@xhs/ark-datacenter": "0.0.9", "@xhs/biz-dict-kit-core": "0.0.3-beta.0", "@xhs/delight": "1.2.15", "@xhs/delight-ark-indicator-card": "^0.3.0", "@xhs/delight-ark-qrcode": "^1.0.1", "@xhs/delight-formily": "0.6.6", "@xhs/delight-material-ads-note-detail": "^0.1.6", "@xhs/delight-material-ark-goods-select": "0.2.15", "@xhs/delight-material-ark-qrcode": "^0.1.8", "@xhs/delight-material-item-calendar": "^0.0.7", "@xhs/delight-material-life-category-selector": "^0.1.3", "@xhs/delight-material-life-enhance-input-number": "^0.1.3", "@xhs/delight-material-life-identification-upload": "0.1.8", "@xhs/delight-material-life-image": "^0.1.2", "@xhs/delight-material-life-qrcode": "^0.1.5", "@xhs/delight-material-life-region-cascader": "0.1.1", "@xhs/delight-material-life-show-qrcode": "^0.1.7", "@xhs/delight-material-life-validity-period": "^0.1.2", "@xhs/delight-material-promotion-pc-feedback": "^0.1.5", "@xhs/delight-material-ultra-delight-form": "^0.1.8", "@xhs/delight-material-ultra-enhance-upload": "0.2.4", "@xhs/delight-material-ultra-image-conversion": "^0.1.13", "@xhs/delight-material-ultra-outline-filter": "^0.1.15", "@xhs/delight-material-ultra-page-header": "^0.2.10", "@xhs/delight-material-ultra-table-cell": "^0.1.30", "@xhs/delight-material-ultra-toolbar": "^0.1.28", "@xhs/delight-material-ultra-tour-guide": "^1.0.4", "@xhs/delight-official-chart": "^1.1.3", "@xhs/di": "^0.1.1", "@xhs/formula-cli": "^3.21.6", "@xhs/launcher-ark": "1.2.16", "@xhs/launcher-plugin-anti-spam": "^3.6.8", "@xhs/modular-startup": "4.2.0", "@xhs/module-reactive": "^0.1.0", "@xhs/ozone-bridge": "3.18.0", "@xhs/reder-icon-svg-ReDs_icon": "^1.0.8", "@xhs/reds-h5-next": "^0.4.0", "@xhs/reds-token-next": "^0.4.0", "@xhs/uploader": "3.0.3", "@xhs/vue-di": "^0.2.0", "axios": "1.5.0", "big.js": "^6.2.1", "dompurify": "^3.0.5", "echarts": "^5.4.3", "html-to-image": "1.11.11", "js-cookie": "2.2.1", "lodash": "4.17.20", "qiankun": "2.4.0", "shared": "^1.0.0", "vue-cropperjs": "^5.0.0", "vuedraggable": "4.0.1"}, "license": "ISC", "workspaces": ["packages/*"], "private": true}