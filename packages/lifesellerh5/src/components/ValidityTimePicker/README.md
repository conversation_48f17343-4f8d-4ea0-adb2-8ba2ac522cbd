# ValidityTimePicker 有效期时间选择组件

一个用于选择有效期的移动端组件，支持永久有效和期限有效两种模式。

## 特性

- 🕒 支持永久有效和期限有效两种模式
- 📅 集成日期选择器，支持起始日期和结束日期选择
- 📱 半浮层设计，优化的移动端交互体验
- 🔄 完整的双向数据绑定支持
- 🔒 支持只读模式
- ✅ 内置表单验证功能，支持30天有效期校验
- 🎨 支持自定义样式和主题，Radio 组件背景色动态调整
- 📐 Radio 组件垂直布局，24px 间距
- 🔧 水平布局的日期选择界面，提升用户体验
- 🚨 实时错误提示和验证反馈

## 使用方法

### 基础用法

```vue
<template>
  <ValidityTimePicker
    v-model="timeValue"
    @change="handleTimeChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import ValidityTimePicker from '~/components/ValidityTimePicker/index.vue'

const timeValue = ref(null)

const handleTimeChange = (value) => {
  console.log('时间选择变化:', value)
}
</script>
```

### 表单验证用法

```vue
<template>
  <ValidityTimePicker
    ref="validityPickerRef"
    v-model="timeValue"
    @change="handleTimeChange"
  />
  <button @click="validateForm">验证表单</button>
</template>

<script setup>
import { ref } from 'vue'
import ValidityTimePicker from '~/components/ValidityTimePicker/index.vue'

const timeValue = ref(null)
const validityPickerRef = ref()

const handleTimeChange = (value) => {
  console.log('时间选择变化:', value)
}

// 表单验证
const validateForm = () => {
  if (validityPickerRef.value) {
    const isValid = validityPickerRef.value.valid()
    if (isValid) {
      console.log('验证通过')
      // 提交表单
    } else {
      console.log('验证失败')
      // 显示错误信息
    }
  }
}
</script>
```

### 只读模式

```vue
<template>
  <ValidityTimePicker
    v-model="timeValue"
    :read-only="true"
    placeholder="查看有效期"
  />
</template>
```

### 自定义显示

```vue
<template>
  <ValidityTimePicker v-model="timeValue">
    <template #display="{ value, formatted }">
      <div class="custom-display">
        <span class="label">有效期：</span>
        <span class="value">{{ formatted || '未设置' }}</span>
      </div>
    </template>
  </ValidityTimePicker>
</template>
```

### 完整示例

```vue
<template>
  <div class="demo">
    <h3>时间选择器示例</h3>
    
    <!-- 基础用法 -->
    <div class="demo-item">
      <h4>基础用法</h4>
      <ValidityTimePicker
        v-model="basicValue"
        placeholder="请选择有效期"
        @change="handleBasicChange"
      />
      <p>当前值: {{ JSON.stringify(basicValue) }}</p>
    </div>

    <!-- 只读模式 -->
    <div class="demo-item">
      <h4>只读模式</h4>
      <ValidityTimePicker
        v-model="readOnlyValue"
        :read-only="true"
        placeholder="只读状态"
      />
    </div>

    <!-- 自定义格式 -->
    <div class="demo-item">
      <h4>自定义日期格式</h4>
      <ValidityTimePicker
        v-model="customFormatValue"
        date-format="YYYY年MM月DD日"
        placeholder="自定义格式"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ValidityTimePicker from '~/components/ValidityTimePicker/index.vue'
import { validityTimePickerUtils } from '~/components/ValidityTimePicker/composable'

const basicValue = ref(null)
const readOnlyValue = ref(timePickerUtils.createPermanentValue())
const customFormatValue = ref(null)

const handleBasicChange = (value) => {
  console.log('基础用法值变化:', value)
  
  // 检查是否过期
  if (timePickerUtils.isExpired(value)) {
    console.warn('选择的日期已过期')
  }
}
</script>
```

## Radio 组件配置

### 组件结构
- **RadioGroup**: 设置为垂直布局 (`RadioGroupType.RadioGroupLayout.VERTICAL`)
- **第一个 Radio**: `value="true"`, 显示文本"永久有效"
- **第二个 Radio**: `value="false"`, 显示文本"期限有效"

### 样式特性
- **间距**: Radio 组件之间 24px margin-bottom
- **背景色**: 根据 `colorMode` 属性动态调整
  - 浅色模式: `#F8F8F8`
  - 深色模式: `rgba(255, 255, 255, 0.1)`

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `ValidityTimePickerValue \| null` | `null` | 双向绑定的值 |
| readOnly | `boolean` | `false` | 是否只读 |
| disabled | `boolean` | `false` | 是否禁用 |
| placeholder | `string` | `'请选择时间段'` | 占位符文本 |
| label | `string` | `'选择有效期'` | 弹窗标题 |
| colorMode | `'light' \| 'dark'` | `'light'` | 颜色模式，影响 Radio 组件背景色 |
| dateFormat | `string` | `'YYYY-MM-DD'` | 日期格式化字符串 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(value: TimePickerValue \| null)` | 值更新事件 |
| change | `(value: TimePickerValue \| null)` | 值变化事件 |

### 方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| valid | 验证表单数据有效性 | - | boolean |

#### valid() 方法说明

`valid()` 方法用于验证当前选择的有效期数据是否符合要求，验证规则如下：

1. **起始日期检查**：检查是否已选择起始日期
2. **结束日期检查**：检查是否已选择结束日期
3. **有效期检查**：验证营业执照有效期剩余时间是否大于30天

**使用示例：**

```javascript
// 通过模板引用调用
const validityPickerRef = ref()
const isValid = validityPickerRef.value.valid()

if (isValid) {
  // 验证通过，可以提交表单
  console.log('验证通过')
} else {
  // 验证失败，错误信息会自动显示在组件中
  console.log('验证失败')
}
```

### Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| display | `{ value: TimePickerValue \| null, formatted: string }` | 自定义显示区域 |

### 类型定义

```typescript
// 有效期类型枚举 - 与参考代码保持一致
enum Period {
  PERMANENT = 0,    // 永久有效
  NON_PERMANENT = 1 // 期限有效
}

interface TimePickerValue {
  /** 有效期类型：0-永久有效，1-期限有效 */
  qualValidityPeriod: Period
  /** 开始时间（期限有效时设为0） */
  startTime?: number
  /** 结束时间（时间戳或日期字符串） */
  endTime?: string | number
}
```

## 工具函数

组件提供了一系列工具函数来帮助处理时间选择器的值：

```typescript
import { validityTimePickerUtils } from '~/components/ValidityTimePicker/composable'

// 验证值是否有效
validityTimePickerUtils.isValidValue(value)

// 格式化显示文本
validityTimePickerUtils.formatDisplayText(value, 'YYYY-MM-DD')

// 创建永久有效的值
validityTimePickerUtils.createPermanentValue()
// 返回: { qualValidityPeriod: 0, startTime: undefined, endTime: undefined }

// 创建期限有效的值
validityTimePickerUtils.createLimitedValue(new Date())
// 返回: { qualValidityPeriod: 1, startTime: 0, endTime: timestamp }

// 检查是否已过期
validityTimePickerUtils.isExpired(value)

// 检查是否有有效的有效期设置
validityTimePickerUtils.hasValid(value)
```

## 样式特性

- 使用 Stylus 预处理器
- 响应式设计，适配移动端
- 遵循设计规范的颜色和尺寸
- 支持暗色模式

## 依赖

- Vue 3
- @xhs/reds-h5-next
- @xhs/onix-icon
- dayjs
- TypeScript
- Stylus

## 注意事项

1. 组件使用了 reds-h5-next 组件库，请确保已正确安装和配置
2. 组件采用 Composition API 编写，支持 TypeScript
3. 样式使用 scoped 作用域，避免样式冲突
4. 日期选择器仅支持日期选择，不包含时间选择
5. 组件会自动生成时间戳，方便后端处理
6. **新增功能**：日期范围选择界面采用水平布局，起始日期和结束日期并排显示
7. **验证功能**：内置30天有效期验证，确保营业执照剩余有效期符合要求
8. **错误提示**：验证失败时会在组件内显示具体的错误信息
9. **方法暴露**：通过 `defineExpose` 暴露 `valid` 方法供父组件调用
10. **实时反馈**：用户选择日期后会自动清除之前的错误信息
