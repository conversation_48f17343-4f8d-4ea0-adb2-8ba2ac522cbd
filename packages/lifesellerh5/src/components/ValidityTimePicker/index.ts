// 导出主组件
export { default as ValidityTimePicker } from './index.vue'

// 导出类型定义
export type {
  ValidityTimePickerValue,
  ValidityTimePickerProps,
  ValidityTimePickerEmits,
  // 向后兼容性类型别名
  TimePickerValue,
  TimePickerProps,
  TimePickerEmits
} from './types'

// 导出枚举和常量
export {
  Period,
  DEFAULT_CONFIG
} from './types'

// 导出组合式函数和工具函数
export {
  useValidityTimePicker,
  validityTimePickerUtils,
  // 向后兼容性函数别名
  useTimePicker,
  timePickerUtils
} from './composable'

// 向后兼容性组件别名
export { default as TimePicker } from './index.vue'

// 默认导出主组件
export { default } from './index.vue'
