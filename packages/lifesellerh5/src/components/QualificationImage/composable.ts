import { ref, computed, watch } from 'vue'
import type { ValidityTimePickerValue } from '../ValidityTimePicker/types'
import type {
  QualificationManagerValue,
  QualificationImageItem,
  BusinessLicenseImageProps
} from './types'
import { DEFAULT_CONFIG } from './types'
import { useQualificationValidity } from '../QualificationCard/composables/useQualificationValidity'
import { Period, LicenseValidItem } from '../QualificationCard/core/type'

/**
 * 数据转换函数
 */
const transformValue = (value?: QualificationManagerValue | null): QualificationManagerValue => {
  if (!value) {
    return {
      validity: null,
      qualificationType: '',
      images: []
    }
  }
  return {
    validity: value.validity || null,
    qualificationType: value.qualificationType || '',
    images: value.images || []
  }
}

/**
 * BusinessLicenseImage组合式函数
 * 重构后使用新的组合式函数架构
 */
export function useBusinessLicenseImage(
  props: BusinessLicenseImageProps,
  emits: any
) {
  // 响应式状态
  const errorMessage = ref('')
  const uploadLoading = ref(false)

  // 内部状态管理
  const internalValue = ref(transformValue(props.modelValue))

  // 转换有效期数据格式
  const convertValidityToLicenseValid = (validity: ValidityTimePickerValue | null): LicenseValidItem | null => {
    if (!validity) return null
    return {
      qualValidityPeriod: validity.qualValidityPeriod,
      startTime: validity.startTime || 0,
      endTime: typeof validity.endTime === 'string' ? new Date(validity.endTime).getTime() : (validity.endTime || 0)
    }
  }

  // 转换有效期数据格式（反向）
  const convertLicenseValidToValidity = (licenseValid: LicenseValidItem | null): ValidityTimePickerValue | null => {
    if (!licenseValid) return null
    return {
      qualValidityPeriod: licenseValid.qualValidityPeriod,
      startTime: licenseValid.startTime,
      endTime: licenseValid.endTime
    }
  }

  // 使用有效期管理组合式函数
  const validityManager = useQualificationValidity(
    convertValidityToLicenseValid(props.validity ?? internalValue.value.validity),
    {
      validateOnChange: true,
      requireValidityWhenHasImages: true,
      requireImagesWhenHasValidity: false
    }
  )

  // 计算属性 - 资质类型
  const qualificationType = computed(() =>
    props.qualificationType ?? internalValue.value.qualificationType)

  // 计算属性 - 有效期（转换格式）
  const validity = computed(() => {
    if (props.validity !== undefined) return props.validity
    return convertLicenseValidToValidity(validityManager.validity.value)
  })

  // 计算属性 - 图片数组
  const images = computed(() =>
    props.images ?? internalValue.value.images)

  // 计算属性 - 当前完整值
  const currentValue = computed<QualificationManagerValue>(() => ({
    validity: validity.value,
    qualificationType: qualificationType.value,
    images: images.value
  }))

  // 计算属性 - 资质类型显示文本
  const qualificationTypeText = computed(() =>
    props.qualificationTypeText || DEFAULT_CONFIG.qualificationTypeText)

  // 计算属性 - 图片上传配置
  const uploadConfig = computed(() => ({
    maxCount: props.maxCount || DEFAULT_CONFIG.maxCount,
    maxSize: props.maxSize || DEFAULT_CONFIG.maxSize,
    accept: props.accept || DEFAULT_CONFIG.accept
  }))

  // 计算属性 - 是否可以上传更多图片
  const canUploadMore = computed(() =>
    images.value.length < uploadConfig.value.maxCount)

  // 计算属性 - 图片上传提示文本
  const uploadTipText = computed(() =>
    `最多上传${uploadConfig.value.maxCount}张JPG/JPEG/PNG格式材料，大小${uploadConfig.value.maxSize}MB以内`)

  // 验证方法（使用新的验证逻辑）
  const valid = (): boolean => {
    clearError()

    // 验证图片
    if (!images.value || images.value.length === 0) {
      errorMessage.value = '请上传资质图片'
      return false
    }

    // 验证图片是否上传完成
    const hasIncompleteImages = images.value.some(img =>
      !img.url || (!img.path && !img.url.startsWith('http'))
    )

    if (hasIncompleteImages) {
      errorMessage.value = '图片上传中，请稍候'
      return false
    }

    // 转换图片格式用于验证
    const imageItems = images.value.map(img => ({
      name: img.id?.toString() || '',
      uid: img.id?.toString() || '',
      size: 0,
      status: 'success' as const,
      width: img.width || 0,
      height: img.height || 0,
      url: img.url,
      path: img.path
    }))

    // 使用有效期管理器进行验证
    const validityValidation = validityManager.validateValidityWithImages(imageItems)
    if (!validityValidation.valid) {
      errorMessage.value = validityValidation.errors[0] || '验证失败'
      return false
    }

    return true
  }

  // 清除错误信息
  const clearError = () => {
    errorMessage.value = ''
  }

  // 同步变更到外部
  const syncChange = () => {
    const newValue = currentValue.value
    emits('update:modelValue', newValue)
    emits('change', newValue)
  }

  // 图片上传处理
  const handleImageUpload = (file: File) => {
    // 文件大小验证
    if (file.size > uploadConfig.value.maxSize * 1024 * 1024) {
      errorMessage.value = `图片大小不能超过${uploadConfig.value.maxSize}MB`
      return
    }

    // 数量验证
    if (images.value.length >= uploadConfig.value.maxCount) {
      errorMessage.value = `最多只能上传${uploadConfig.value.maxCount}张图片`
      return
    }

    clearError()
    uploadLoading.value = true

    // 创建图片对象
    const imageUrl = URL.createObjectURL(file)
    const newImage: QualificationImageItem = {
      url: imageUrl,
      file,
      path: imageUrl,
      id: Date.now() + Math.random()
    }

    // 更新图片列表
    const newImages = [...images.value, newImage]
    internalValue.value.images = newImages
    emits('update:images', newImages)

    // 触发上传事件
    emits('upload', file)

    uploadLoading.value = false
    syncChange()
  }

  // 图片删除处理
  const handleImageDelete = (image: QualificationImageItem, index: number) => {
    // 释放内存中的URL
    if (image.url && image.url.startsWith('blob:')) {
      URL.revokeObjectURL(image.url)
    }

    // 更新图片列表
    const newImages = images.value.filter((_, i) => i !== index)
    internalValue.value.images = newImages
    emits('update:images', newImages)

    // 触发删除事件
    emits('delete', image, index)

    clearError()
    syncChange()
  }

  // 图片预览处理
  const handleImagePreview = (image: QualificationImageItem) => {
    console.log('Preview image:', image)
  }

  // 有效期变化处理（使用新的有效期管理器）
  const handleValidityChange = (newValidity: ValidityTimePickerValue | null) => {
    // 更新内部状态
    internalValue.value.validity = newValidity

    // 更新有效期管理器
    const licenseValid = convertValidityToLicenseValid(newValidity)
    validityManager.updateValidity(licenseValid)

    // 触发外部事件
    emits('update:validity', newValidity)
    syncChange()
  }

  // 重置组件（包括有效期管理器）
  const reset = () => {
    internalValue.value = transformValue(null)
    validityManager.reset()
    errorMessage.value = ''
    uploadLoading.value = false
  }

  // 获取当前值
  const getValue = (): QualificationManagerValue | null => currentValue.value

  // 设置值（同步到有效期管理器）
  const setValue = (value: QualificationManagerValue | null) => {
    internalValue.value = transformValue(value)

    // 同步有效期到管理器
    const licenseValid = convertValidityToLicenseValid(value?.validity || null)
    validityManager.updateValidity(licenseValid)
  }

  // 清除验证状态（包括有效期管理器）
  const clearValidation = () => {
    clearError()
    validityManager.validationErrors.value = []
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue !== currentValue.value) {
        internalValue.value = transformValue(newValue)
      }
    },
    { deep: true }
  )

  return {
    // 响应式状态
    errorMessage,
    uploadLoading,

    // 计算属性
    qualificationType,
    validity,
    images,
    currentValue,
    qualificationTypeText,
    uploadConfig,
    canUploadMore,
    uploadTipText,

    // 方法
    valid,
    clearError,
    handleImageUpload,
    handleImageDelete,
    handleImagePreview,
    handleValidityChange,
    reset,
    getValue,
    setValue,
    clearValidation,

    // 有效期管理器相关
    validityManager,
    isPermanent: validityManager.isPermanent,
    isNonPermanent: validityManager.isNonPermanent,
    hasValidValidity: validityManager.hasValidValidity,
    validityDisplayText: validityManager.validityDisplayText,
    isExpiringSoon: validityManager.isExpiringSoon
  }
}
