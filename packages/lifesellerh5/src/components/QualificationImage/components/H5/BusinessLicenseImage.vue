<template>
  <div class="business-license-image-wrapper">
    <!-- 资质类型显示区域 -->
    <div class="qualification-type-section">
      <div class="qualification-type-display">
        <span class="qualification-type-label">资质类型</span>
        <span class="qualification-type-text">{{ qualificationTypeText }}</span>
      </div>
    </div>

    <!-- 证明材料区域 -->
    <div class="qualification-materials-section">
      <div class="materials-title">{{ dictFieldModel.title }}</div>
      <div class="materials-tip">{{ dictFieldModel.description || uploadTipText }}</div>

      <!-- 图片预览网格 - 由于ImageUpload组件自带预览功能，这里可以简化或移除 -->
      <div v-if="images.length > 0 && readOnly" class="image-grid">
        <div
          v-for="(image, index) in images"
          :key="image.id || index"
          class="image-item"
        >
          <div class="image-preview">
            <Image
              :src="image.url"
              :alt="`资质图片${index + 1}`"
              fit="cover"
              @click="handleImagePreview(image)"
            />
            <div
              v-if="!readOnly && !disabled"
              class="image-delete-btn"
              @click="handleImageDelete(image, index)"
            >
              <OnixIcon icon="xCircleFilledS" />
            </div>
          </div>
        </div>
      </div>

      <!-- 图片上传区域 -->
      <div class="upload-section">
        <Uploader
          v-model="images"
          :is-preview="readOnly"
          :max-count="uploadConfig.maxCount"
          :max-size="uploadConfig.maxSize"
          :prohibit-operation="readOnly || disabled"
          @update:model-value="handleImageListUpdate"
        />
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage || validationError" class="error-message">
        {{ errorMessage || validationError }}
      </div>
    </div>

    <!-- 有效期区域 -->
    <div class="validity-section">
      <ValidityTimePicker
        :model-value="validity"
        :read-only="readOnly"
        :disabled="disabled"
        v-bind="validityProps"
        @update:model-value="handleValidityChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
    // Vue相关导入
  import { ref, watch } from 'vue'

  // 组件库导入
  import { Image } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import Uploader from '~/pages/ClaimStorePage/components/ImageUpload/index.vue'
  // 本地组件导入
  import ValidityTimePicker from '../../../ValidityTimePicker/index.vue'

  // 类型和组合式函数导入
  import type {
    BusinessLicenseImageProps,
    QualificationManagerValue,
    QualificationImageItem,
    BusinessLicenseImageInstance
    } from '../../types'
  import type { ValidityTimePickerValue } from '../../../ValidityTimePicker/types'
  import { useBusinessLicenseImage } from '../../composable'

  // Core导入 - model和validator
  import { DictField } from '../../core/model'
  import { imagesValidator, getValidator } from '../../core/validator'

  // // 静态资源导入
  // import '../../../../assets/svg/xCircleFilledS.svg'
  // import '../../../../assets/svg/plusCircleM.svg'

  // Props定义
  const props = withDefaults(defineProps<BusinessLicenseImageProps>(), {
    modelValue: null,
    qualificationType: '',
    validity: null,
    images: () => [],
    readOnly: false,
    disabled: false,
    maxCount: 5,
    maxSize: 5,
    accept: '.jpg,.jpeg,.png',
    qualificationTypeText: '特种行业经营许可证',
    validityProps: () => ({})
  })

  // Emits定义
  const emits = defineEmits<{
    'update:modelValue': [value: QualificationManagerValue | null]
    'update:qualificationType': [value: string]
    'update:validity': [value: ValidityTimePickerValue | null]
    'update:images': [value: QualificationImageItem[]]
    'change': [value: QualificationManagerValue | null]
    'upload': [file: File]
    'delete': [image: QualificationImageItem, index: number]
  }>()

  // 使用组合式函数
  const {
    // 响应式状态
    errorMessage,

    // 计算属性
    validity,
    images,
    qualificationTypeText,
    uploadConfig,
    uploadTipText,

    // 方法
    valid,
    handleImageDelete,
    handleImagePreview,
    handleValidityChange,
    reset,
    getValue,
    setValue,
    clearValidation
  } = useBusinessLicenseImage(props, emits)

    // Core相关状态
  const validationError = ref('')

  // 创建和管理DictField model实例
  const dictFieldModel = ref<DictField>(new DictField({
    name: 'mediaInfoList',
    title: '资质图片',
    description: '图片格式支持PNG/JPG/JPEG格式，尺寸800*800px以上，大小5M以内，每个资质最多上传5张',
    required: !props.readOnly && !props.disabled,
    disabled: props.disabled,
    validator: getValidator(!props.readOnly && !props.disabled)
  }))

  // 同步model的状态
  const syncModelState = () => {
    const model = dictFieldModel.value
    model.value = images.value as any
    model.required = !props.readOnly && !props.disabled
    model.disabled = props.disabled
    model.validator = getValidator(!props.readOnly && !props.disabled)
  }

  // 使用core validator进行图片校验
  const validateImages = (imagesToValidate: QualificationImageItem[] = []) => {
    validationError.value = ''

    if (props.readOnly || props.disabled) {
      return true
    }

    try {
      // 转换数据格式以适配validator
      const formattedImages = imagesToValidate.map(img => ({
        link: img.link || img.url || '',
        width: img.width || 0,
        height: img.height || 0,
        path: img.path,
        url: img.url
      }))

      const result = imagesValidator(formattedImages, !props.readOnly && !props.disabled)

      if (result instanceof Error) {
        validationError.value = result.message
        return false
      }

      return true
    } catch (error) {
      validationError.value = '图片校验失败'
      return false
    }
  }

    // 使用model进行校验
  const validateWithModel = (): boolean => {
    syncModelState()
    const model = dictFieldModel.value

    if (model.validator && model.value) {
      const rules = Array.isArray(model.validator) ? model.validator : [model.validator]
      for (const rule of rules) {
        if (rule.validator) {
          const result = rule.validator(rule, model.value, (error: any) => {
            if (error) {
              validationError.value = error.message || '校验失败'
            }
          }, {}, {})
          if (result instanceof Error) {
            validationError.value = result.message
            return false
          }
        }
      }
    }

    return true
  }

  // 增强的校验方法
  const enhancedValid = (): boolean => {
    // 清除之前的错误
    validationError.value = ''

    // 首先执行原有的校验
    const basicValidation = valid()

    // 然后执行core validator的校验
    const coreValidation = validateImages(images.value)

    // 最后执行model校验
    const modelValidation = validateWithModel()

    return basicValidation && coreValidation && modelValidation
  }

  // 处理ImageUpload组件的数据更新
  const handleImageListUpdate = (newImages: any[]) => {
    // 将ImageUpload组件的数据格式转换为我们的数据格式
    const formattedImages = newImages.map((item, index) => ({
      url: item.url || item.link || '',
      file: item.file,
      link: item.link,
      width: item.width,
      height: item.height,
      path: item.path,
      id: item.id || `${Date.now()}_${index}`
    }))

    // 实时校验图片
    validateImages(formattedImages)

    // 同步model状态
    syncModelState()

    // 更新内部状态
    emits('update:images', formattedImages)
    emits('update:modelValue', {
      validity: validity.value,
      qualificationType: props.qualificationType || '',
      images: formattedImages
    })
  }

  // 清除所有验证状态
  const clearAllValidation = () => {
    validationError.value = ''
    clearValidation()
  }

  // 重置组件时也清除core validation
  const enhancedReset = () => {
    validationError.value = ''
    reset()

    // 重置model状态
    const model = dictFieldModel.value
    model.value = undefined
    model.initialValue = undefined
  }

  // 增强的setValue方法
  const enhancedSetValue = (value: QualificationManagerValue | null) => {
    setValue(value)

    // 同步model状态
    syncModelState()
  }

  // 监听images变化，实时校验
  watch(
    () => images.value,
    newImages => {
      // 延迟校验，避免频繁触发
      setTimeout(() => {
        validateImages(newImages)
        syncModelState()
      }, 100)
    },
    { deep: true }
  )

  // 监听props变化，同步到model
  watch(
    [() => props.readOnly, () => props.disabled],
    () => {
      syncModelState()
    }
  )

  // 暴露给父组件的方法和model实例
  defineExpose<BusinessLicenseImageInstance & { model: DictField }>({
    valid: enhancedValid,
    getValue,
    setValue: enhancedSetValue,
    reset: enhancedReset,
    clearValidation: clearAllValidation,
    model: dictFieldModel.value
  })
</script>

<style scoped lang="stylus">
.business-license-image-wrapper
  padding 0 16px
  color: var(--title);
  font-size: var(--b1-font-size);
  background: var(--bg);
  border-radius: 12px;

.qualification-type-section
  margin-bottom 24px

.qualification-type-display
  display flex
  align-items center
  justify-content space-between
  border-bottom 1px solid #f0f0f0
  padding 12px 0

.qualification-type-label
  font-weight 500

.qualification-type-text
  color: var(--paragraph);

.qualification-materials-section
  margin-bottom 24px

.materials-title
  font-weight 500
  margin-bottom 8px

.materials-tip
  font-size: 12px;
  color: var(--Light-Labels-Description, rgba(0, 0, 0, 0.45));
  margin-bottom 16px

.image-grid
  display grid
  grid-template-columns repeat(3, 1fr)
  gap 8px
  margin-bottom 16px

.image-item
  position relative
  aspect-ratio 1
  border-radius 8px
  overflow hidden

.image-preview
  position relative
  width 100%
  height 100%
  border 1px solid #e0e0e0
  border-radius 8px
  overflow hidden

.image-delete-btn
  position absolute
  top 4px
  right 4px
  width 20px
  height 20px
  border-radius 50%
  display flex
  align-items center
  justify-content center
  z-index 10

.upload-section
  margin-bottom 16px

.error-message
  padding 8px 12px
  color #f56565
  border-radius 6px
  margin-top 8px

.validity-section
  // ValidityTimePicker组件已有样式，无需额外设置
</style>
