<template>
  <div class="qualification-image-wrapper">
    <!-- 图片上传和预览区域 -->
    <div class="image-section">
      <!-- 标题和描述 -->
      <div v-if="title" class="image-title">{{ title }}</div>
      <div v-if="description" class="image-description">{{ description }}</div>

      <!-- 图片预览网格（只读模式） -->
      <div v-if="currentImages.length > 0 && readOnly" class="image-grid">
        <div
          v-for="(image, index) in currentImages"
          :key="image.uid || index"
          class="image-item"
        >
          <div class="image-preview">
            <Image
              :src="image.url"
              :alt="`图片${index + 1}`"
              fit="cover"
              @click="handleImagePreview(image)"
            />
          </div>
        </div>
      </div>

      <!-- 图片上传区域 -->
      <div v-if="!readOnly" class="upload-section">
        <Uploader
          v-model="currentImages"
          :is-preview="false"
          :max-count="maxCount"
          :max-size="maxSize"
          :prohibit-operation="disabled"
          @update:model-value="handleImageListUpdate"
        />
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // Vue相关导入
  import { ref, computed, watch } from 'vue'

  // 组件库导入
  import { Image } from '@xhs/reds-h5-next'
  import Uploader from '~/pages/ClaimStorePage/components/ImageUpload/index.vue'

  // 类型导入
  import type { QualificationImageItem } from '../../types'

  // 简化的Props接口
  interface QualificationImageProps {
    modelValue?: QualificationImageItem[]
    title?: string
    description?: string
    readOnly?: boolean
    disabled?: boolean
    maxCount?: number
    maxSize?: number
    accept?: string
  }

  // Props定义 - 专注于图片功能
  const props = withDefaults(defineProps<QualificationImageProps>(), {
    modelValue: () => [],
    title: '资质图片',
    description: '图片格式支持PNG/JPG/JPEG格式，尺寸800*800px以上，大小5M以内，每个资质最多上传5张',
    readOnly: false,
    disabled: false,
    maxCount: 5,
    maxSize: 5,
    accept: '.jpg,.jpeg,.png'
  })

  // Emits定义 - 简化为图片相关事件
  const emits = defineEmits<{
    'update:modelValue': [value: QualificationImageItem[]]
    'change': [value: QualificationImageItem[]]
    'upload': [file: File]
    'delete': [image: QualificationImageItem, index: number]
    'preview': [image: QualificationImageItem]
  }>()

  // 响应式状态
  const errorMessage = ref('')

  // 计算属性 - 当前图片列表
  const currentImages = computed({
    get: () => props.modelValue || [],
    set: (newImages: QualificationImageItem[]) => {
      emits('update:modelValue', newImages)
      emits('change', newImages)
    }
  })

  // 基础图片验证
  const validateImages = (imagesToValidate: QualificationImageItem[] = []): boolean => {
    errorMessage.value = ''

    if (props.readOnly || props.disabled) {
      return true
    }

    // 基础验证：检查图片格式和大小
    for (const image of imagesToValidate) {
      // 检查图片URL是否有效
      if (!image.url && !image.path) {
        errorMessage.value = '图片上传失败，请重新上传'
        return false
      }

      // 检查图片大小（如果有size信息）
      if (image.file && image.file.size > props.maxSize * 1024 * 1024) {
        errorMessage.value = `图片大小不能超过${props.maxSize}MB`
        return false
      }
    }

    // 检查图片数量
    if (imagesToValidate.length > props.maxCount) {
      errorMessage.value = `最多只能上传${props.maxCount}张图片`
      return false
    }

    return true
  }

  // 图片预览处理
  const handleImagePreview = (image: QualificationImageItem) => {
    emits('preview', image)
  }

  // 处理图片列表更新
  const handleImageListUpdate = (newImages: any[]) => {
    // 将上传组件的数据格式转换为我们的数据格式
    const formattedImages: QualificationImageItem[] = newImages.map((item, index) => ({
      url: item.url || item.link || '',
      file: item.file,
      link: item.link,
      width: item.width || 0,
      height: item.height || 0,
      path: item.path,
      uid: item.uid || `${Date.now()}_${index}`
    }))

    // 验证图片
    if (!validateImages(formattedImages)) {
      return
    }

    // 更新图片数据
    currentImages.value = formattedImages

    // 触发上传事件（如果有新文件）
    formattedImages.forEach(image => {
      if (image.file) {
        emits('upload', image.file)
      }
    })
  }

  // 删除图片
  const handleImageDelete = (image: QualificationImageItem, index: number) => {
    const newImages = [...currentImages.value]
    newImages.splice(index, 1)
    currentImages.value = newImages
    emits('delete', image, index)
  }

  // 验证方法
  const valid = (): boolean => {
    return validateImages(currentImages.value)
  }

  // 重置组件
  const reset = () => {
    currentImages.value = []
    errorMessage.value = ''
  }

  // 清除验证状态
  const clearValidation = () => {
    errorMessage.value = ''
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue && newValue !== currentImages.value) {
        // 验证新值
        validateImages(newValue)
      }
    },
    { deep: true }
  )

  // 简化的组件实例接口
  interface QualificationImageInstance {
    valid: () => boolean
    reset: () => void
    clearValidation: () => void
  }

  // 暴露给父组件的方法
  defineExpose<QualificationImageInstance>({
    valid,
    reset,
    clearValidation
  })
</script>

<style scoped lang="stylus">
.business-license-image-wrapper
  padding 0 16px
  color: var(--title);
  font-size: var(--b1-font-size);
  background: var(--bg);
  border-radius: 12px;

.qualification-type-section
  margin-bottom 24px

.qualification-type-display
  display flex
  align-items center
  justify-content space-between
  border-bottom 1px solid #f0f0f0
  padding 12px 0

.qualification-type-label
  font-weight 500

.qualification-type-text
  color: var(--paragraph);

.qualification-materials-section
  margin-bottom 24px

.materials-title
  font-weight 500
  margin-bottom 8px

.materials-tip
  font-size: 12px;
  color: var(--Light-Labels-Description, rgba(0, 0, 0, 0.45));
  margin-bottom 16px

.image-grid
  display grid
  grid-template-columns repeat(3, 1fr)
  gap 8px
  margin-bottom 16px

.image-item
  position relative
  aspect-ratio 1
  border-radius 8px
  overflow hidden

.image-preview
  position relative
  width 100%
  height 100%
  border 1px solid #e0e0e0
  border-radius 8px
  overflow hidden

.image-delete-btn
  position absolute
  top 4px
  right 4px
  width 20px
  height 20px
  border-radius 50%
  display flex
  align-items center
  justify-content center
  z-index 10

.upload-section
  margin-bottom 16px

.error-message
  padding 8px 12px
  color #f56565
  border-radius 6px
  margin-top 8px

.validity-section
  // ValidityTimePicker组件已有样式，无需额外设置
</style>
