# BusinessLicenseImage 组件

一个用于管理资质信息的Vue组件，集成了有效期选择、资质类型管理和图片上传预览功能。

## 功能特性

- ✅ 资质类型显示
- ✅ 图片上传和预览（支持多张图片）
- ✅ 图片删除功能
- ✅ 有效期选择（集成ValidityTimePicker组件）
- ✅ 完整的表单验证
- ✅ 双向数据绑定
- ✅ 只读和禁用状态支持

## 基本用法

```vue
<template>
  <div>
    <!-- 完整数据对象绑定 -->
    <BusinessLicenseImage 
      v-model="qualificationData"
      @change="handleQualificationChange"
    />
    
    <!-- 分字段绑定 -->
    <BusinessLicenseImage 
      v-model:qualification-type="qualificationType"
      v-model:validity="validity"
      v-model:images="images"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BusinessLicenseImage } from '@/components/QualificationImage'
import type { QualificationManagerValue } from '@/components/QualificationImage'

// 完整数据对象
const qualificationData = ref<QualificationManagerValue>({
  validity: null,
  qualificationType: '特种行业经营许可证',
  images: []
})

// 分字段数据
const qualificationType = ref('食品经营许可证')
const validity = ref(null)
const images = ref([])

const handleQualificationChange = (value: QualificationManagerValue | null) => {
  console.log('资质信息变更:', value)
}
</script>
```

## API 参考

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `QualificationManagerValue \| null` | `null` | 完整数据对象的双向绑定 |
| `qualificationType` | `string` | `''` | 资质类型双向绑定 |
| `validity` | `ValidityTimePickerValue \| null` | `null` | 有效期双向绑定 |
| `images` | `QualificationImageItem[]` | `[]` | 图片数据双向绑定 |
| `readOnly` | `boolean` | `false` | 是否只读 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `maxCount` | `number` | `5` | 图片上传最大数量 |
| `maxSize` | `number` | `5` | 单个图片最大文件大小(MB) |
| `accept` | `string` | `'.jpg,.jpeg,.png'` | 支持的图片格式 |
| `qualificationTypeText` | `string` | `'特种行业经营许可证'` | 资质类型显示文本 |
| `validityProps` | `Record<string, any>` | `{}` | 有效期组件的额外配置 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: QualificationManagerValue \| null)` | 完整数据对象更新 |
| `update:qualificationType` | `(value: string)` | 资质类型更新 |
| `update:validity` | `(value: ValidityTimePickerValue \| null)` | 有效期更新 |
| `update:images` | `(value: QualificationImageItem[])` | 图片数据更新 |
| `change` | `(value: QualificationManagerValue \| null)` | 数据变化事件 |
| `upload` | `(file: File)` | 图片上传事件 |
| `delete` | `(image: QualificationImageItem, index: number)` | 图片删除事件 |

### 暴露的方法

通过模板引用访问组件实例方法：

```vue
<template>
  <BusinessLicenseImage ref="qualificationRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const qualificationRef = ref()

// 验证表单
const isValid = qualificationRef.value?.valid()

// 获取当前值
const currentValue = qualificationRef.value?.getValue()

// 设置值
qualificationRef.value?.setValue(newValue)

// 重置组件
qualificationRef.value?.reset()

// 清除验证状态
qualificationRef.value?.clearValidation()
</script>
```

## 数据类型

### QualificationManagerValue

```typescript
interface QualificationManagerValue {
  /** 有效期信息 */
  validity: ValidityTimePickerValue | null
  /** 资质类型字符串 */
  qualificationType: string
  /** 图片数组 */
  images: QualificationImageItem[]
}
```

### QualificationImageItem

```typescript
interface QualificationImageItem {
  /** 图片URL */
  url: string
  /** 图片文件对象 */
  file?: File
  /** 图片链接地址 */
  link?: string
  /** 图片宽度 */
  width?: number
  /** 图片高度 */
  height?: number
  /** 图片路径 */
  path?: string
  /** 图片ID */
  id?: string | number
}
```

## 样式自定义

组件使用作用域样式，可以通过以下CSS变量进行自定义：

```css
.business-license-image-wrapper {
  --qualification-bg-color: #fff;
  --qualification-text-color: #333;
  --qualification-border-color: #f0f0f0;
  --upload-bg-color: #fafafa;
  --upload-border-color: #d0d0d0;
  --error-bg-color: #ffeaea;
  --error-text-color: #f56565;
}
```

## 注意事项

1. 组件依赖 `@xhs/reds-h5-next`、`@xhs/onix-icon` 和项目内部的 `ImageUpload` 组件，请确保已正确安装
2. 组件内部集成了 `ValidityTimePicker` 组件，确保路径正确
3. 使用项目自定义的 `ImageUpload` 组件替代了标准的 `Uploader`，具有更好的集成性
4. `ImageUpload` 组件自带预览功能，在只读模式下会自动切换到预览状态
5. 建议在表单提交前调用 `valid()` 方法进行验证
6. 组件支持完整对象绑定和分字段绑定两种方式，推荐使用完整对象绑定以保持数据一致性

## 更新日志

### v1.1.0

- 🔄 **重要更新**：替换图片上传组件
- ✅ 使用项目内部的 `ImageUpload` 组件替代 `@xhs/reds-h5-next` 的 `Uploader`
- ✅ 支持 `isPreview` 模式，只读状态下自动切换到预览
- ✅ 支持 `prohibitOperation` 属性，更好的权限控制
- ✅ 优化数据格式转换逻辑
- ✅ 简化组件结构，移除冗余的预览网格

### v1.0.0

- 🎉 首次发布
- ✅ 支持资质类型显示
- ✅ 支持图片上传预览
- ✅ 集成有效期选择
- ✅ 完整的表单验证功能 