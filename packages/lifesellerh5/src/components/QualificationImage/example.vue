<template>
  <div class="qualification-example">
    <h2>资质信息管理组件示例</h2>

    <!-- 基本用法 -->
    <div class="example-section">
      <h3>基本用法</h3>
      <BusinessLicenseImage
        v-model="basicData"
        @change="handleBasicChange"
      />
      <div class="data-display">
        <h4>当前数据：</h4>
        <pre>{{ JSON.stringify(basicData, null, 2) }}</pre>
      </div>
    </div>

    <!-- 分字段绑定 -->
    <div class="example-section">
      <h3>分字段绑定</h3>
      <BusinessLicenseImage
        v-model:qualification-type="separateQualificationType"
        v-model:validity="separateValidity"
        v-model:images="separateImages"
        :qualification-type-text="separateQualificationType"
        @change="handleSeparateChange"
      />
      <div class="data-display">
        <h4>分字段数据：</h4>
        <p>资质类型：{{ separateQualificationType }}</p>
        <p>有效期：{{ separateValidity }}</p>
        <p>图片数量：{{ separateImages.length }}</p>
      </div>
    </div>

    <!-- 只读模式 -->
    <div class="example-section">
      <h3>只读模式</h3>
      <BusinessLicenseImage
        v-model="readOnlyData"
        :read-only="true"
      />
    </div>

    <!-- 禁用状态 -->
    <div class="example-section">
      <h3>禁用状态</h3>
      <BusinessLicenseImage
        v-model="disabledData"
        :disabled="true"
      />
    </div>

    <!-- 自定义配置 -->
    <div class="example-section">
      <h3>自定义配置</h3>
      <BusinessLicenseImage
        v-model="customData"
        :max-count="3"
        :max-size="2"
        :qualification-type-text="'食品经营许可证'"
        accept=".jpg,.png"
        @upload="handleUpload"
        @delete="handleDelete"
      />
    </div>

    <!-- 表单验证示例 -->
    <div class="example-section">
      <h3>表单验证</h3>
      <BusinessLicenseImage
        ref="validationRef"
        v-model="validationData"
      />
      <div class="validation-buttons">
        <button @click="validateForm">验证表单</button>
        <button @click="resetForm">重置表单</button>
        <button @click="clearValidation">清除验证</button>
      </div>
      <div v-if="validationResult !== null" class="validation-result">
        <p :class="{ 'success': validationResult, 'error': !validationResult }">
          {{ validationResult ? '验证通过' : '验证失败' }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { BusinessLicenseImage } from './index'
  import type {
    QualificationManagerValue,
    QualificationImageItem
    } from './types'
  import type { ValidityTimePickerValue } from '../ValidityTimePicker/types'

  // 基本用法数据
  const basicData = ref<QualificationManagerValue>({
    validity: null,
    qualificationType: '特种行业经营许可证',
    images: []
  })

  // 分字段绑定数据
  const separateQualificationType = ref('食品经营许可证')
  const separateValidity = ref<ValidityTimePickerValue | null>(null)
  const separateImages = ref<QualificationImageItem[]>([])

  // 只读模式数据
  const readOnlyData = ref<QualificationManagerValue>({
    validity: {
      qualValidityPeriod: 1,
      startTime: Date.now(),
      endTime: Date.now() + 365 * 24 * 60 * 60 * 1000
    },
    qualificationType: '特种行业经营许可证',
    images: [
      {
        url: 'https://example.com/image1.jpg',
        id: 1
      }
    ]
  })

  // 禁用状态数据
  const disabledData = ref<QualificationManagerValue>({
    validity: null,
    qualificationType: '特种行业经营许可证',
    images: []
  })

  // 自定义配置数据
  const customData = ref<QualificationManagerValue>({
    validity: null,
    qualificationType: '食品经营许可证',
    images: []
  })

  // 表单验证数据
  const validationData = ref<QualificationManagerValue>({
    validity: null,
    qualificationType: '特种行业经营许可证',
    images: []
  })

  const validationRef = ref()
  const validationResult = ref<boolean | null>(null)

  // 事件处理函数
  const handleBasicChange = (value: QualificationManagerValue | null) => {
    console.log('基本数据变更:', value)
  }

  const handleSeparateChange = (value: QualificationManagerValue | null) => {
    console.log('分字段数据变更:', value)
  }

  const handleUpload = (file: File) => {
    console.log('文件上传:', file.name)
  }

  const handleDelete = (image: QualificationImageItem, index: number) => {
    console.log('图片删除:', image, index)
  }

  // 表单验证方法
  const validateForm = () => {
    validationResult.value = validationRef.value?.valid() || false
  }

  const resetForm = () => {
    validationRef.value?.reset()
    validationResult.value = null
  }

  const clearValidation = () => {
    validationRef.value?.clearValidation()
    validationResult.value = null
  }
</script>

<style scoped lang="stylus">
.qualification-example
  padding 20px
  max-width 800px
  margin 0 auto

.example-section
  margin-bottom 40px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px

.example-section h3
  margin-top 0
  color #333
  border-bottom 1px solid #f0f0f0
  padding-bottom 10px

.data-display
  margin-top 20px
  padding 15px
  background #f9f9f9
  border-radius 6px

.data-display h4
  margin-top 0
  color #555

.data-display pre
  background #fff
  padding 10px
  border-radius 4px
  overflow-x auto
  font-size 12px

.validation-buttons
  margin-top 15px
  display flex
  gap 10px

.validation-buttons button
  padding 8px 16px
  border 1px solid #d0d0d0
  border-radius 4px
  background #fff
  cursor pointer

.validation-buttons button:hover
  background #f5f5f5

.validation-result
  margin-top 10px

.validation-result .success
  color #52c41a

.validation-result .error
  color #f5222d
</style>
