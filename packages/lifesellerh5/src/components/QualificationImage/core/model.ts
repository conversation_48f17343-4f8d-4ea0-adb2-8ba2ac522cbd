import { Field } from 'shared/dictMaterials/runtime/model'

export class DictField extends Field<string> {
  name = 'mediaInfoList'

  title = '资质图片'

  description: string | undefined = '图片格式支持PNG/JPG/JPEG格式，尺寸800*800px以上，大小5M以内，每个资质最多上传5张'

  value = undefined

  initialValue = undefined

  required = false

  disabled = false

  constructor(props: Partial<DictField>) {
    super(props)
    this.name = props.name ?? this.name
    this.title = props.title ?? this.title
    this.validator = props.validator || this.validator
  }
}
