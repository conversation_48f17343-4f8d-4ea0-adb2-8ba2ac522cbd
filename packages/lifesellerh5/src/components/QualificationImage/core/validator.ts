import { FormItemRule } from '@xhs/delight'

interface ImageItem {
  link: string
  width: number
  height: number
  path?: string
  url?: string
}

// 图片格式
export const imagesValidator = (images: ImageItem[] = [], required = true) => {
  if (required && !images?.length) {
    return new Error('请上传图片')
  }
  if (images.some(item => !item.path && !item.url)) {
    return new Error('上传中...')
  }
  // if (images?.length !== 2) {
  //   return new Error('请编辑门店头图后再提交')
  // }
  return true
}

export const notRequiredValidator:FormItemRule[] = [
  {
    validator: (rules, value) => imagesValidator(value, false)
  },
]

export const validator:FormItemRule[] = [
  {
    required: true,
    trigger: 'change',
    message: '请上传资质图片',
  },
  {
    validator: (rules, value) => imagesValidator(value)
  },
]

export const getValidator = (required: boolean) => (required ? validator : notRequiredValidator)
