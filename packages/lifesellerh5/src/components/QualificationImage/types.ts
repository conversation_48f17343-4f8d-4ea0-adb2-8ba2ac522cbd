import type { ValidityTimePickerValue } from '../ValidityTimePicker/types'

// 图片对象接口
export interface QualificationImageItem {
  /** 图片URL */
  url: string
  /** 图片文件对象 */
  file?: File
  /** 图片链接地址 */
  link?: string
  /** 图片宽度 */
  width?: number
  /** 图片高度 */
  height?: number
  /** 图片路径 */
  path?: string
  /** 图片ID */
  id?: string | number
  /** 唯一标识符 */
  uid?: string
}

// 资质管理器的值类型
export interface QualificationManagerValue {
  /** 有效期信息 */
  validity: ValidityTimePickerValue | null
  /** 资质类型字符串 */
  qualificationType: string
  /** 图片数组 */
  images: QualificationImageItem[]
}

// 资质管理器的属性类型
export interface BusinessLicenseImageProps {
  /** 双向绑定的值 */
  modelValue?: QualificationManagerValue | null
  /** 资质类型双向绑定 */
  qualificationType?: string
  /** 有效期双向绑定 */
  validity?: ValidityTimePickerValue | null
  /** 图片数据双向绑定 */
  images?: QualificationImageItem[]
  /** 是否只读 */
  readOnly?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 图片上传最大数量 */
  maxCount?: number
  /** 单个图片最大文件大小 (MB) */
  maxSize?: number
  /** 支持的图片格式 */
  accept?: string
  /** 资质类型显示文本 */
  qualificationTypeText?: string
  /** 有效期组件配置 */
  validityProps?: Record<string, any>
}

// 资质管理器的事件类型
export interface BusinessLicenseImageEmits {
  /** 值更新事件 */
  'update:modelValue': [value: QualificationManagerValue | null]
  /** 资质类型更新事件 */
  'update:qualificationType': [value: string]
  /** 有效期更新事件 */
  'update:validity': [value: ValidityTimePickerValue | null]
  /** 图片数据更新事件 */
  'update:images': [value: QualificationImageItem[]]
  /** 值变化事件 */
  'change': [value: QualificationManagerValue | null]
  /** 图片上传事件 */
  'upload': [file: File]
  /** 图片删除事件 */
  'delete': [image: QualificationImageItem, index: number]
}

// 默认配置
export const DEFAULT_CONFIG = {
  maxCount: 5,
  maxSize: 5, // 5MB
  accept: '.jpg,.jpeg,.png',
  qualificationTypeText: '特种行业经营许可证'
}

// 上传状态枚举
export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 验证规则接口
export interface ValidationRule {
  required?: boolean
  message?: string
  validator?: (value: any) => boolean | string | Promise<boolean | string>
}

// 组件暴露的方法接口
export interface BusinessLicenseImageInstance {
  /** 验证表单数据 */
  valid: () => boolean
  /** 获取当前值 */
  getValue: () => QualificationManagerValue | null
  /** 设置值 */
  setValue: (value: QualificationManagerValue | null) => void
  /** 重置组件 */
  reset: () => void
  /** 清除验证状态 */
  clearValidation: () => void
}
