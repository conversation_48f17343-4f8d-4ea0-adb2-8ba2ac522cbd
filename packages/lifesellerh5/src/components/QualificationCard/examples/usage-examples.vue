<template>
  <div class="usage-examples">
    <h1>资质管理组件使用示例</h1>
    
    <!-- 示例1: QualificationAndItem (AND逻辑) -->
    <section class="example-section">
      <h2>示例1: QualificationAndItem (AND逻辑 - 单选互斥)</h2>
      <p>用户必须且只能选择一种资质类型，选择新类型会清除旧数据</p>
      
      <div class="example-demo">
        <QualificationAndItem
          v-model="andQualificationData"
          :elements="qualificationElements"
          :readonly="false"
          :disabled="false"
          name="andQualificationMap"
        />
      </div>
      
      <div class="example-data">
        <h3>当前数据:</h3>
        <pre>{{ JSON.stringify(andQualificationData, null, 2) }}</pre>
      </div>
    </section>

    <!-- 示例2: QualificationOrItem (OR逻辑) -->
    <section class="example-section">
      <h2>示例2: QualificationOrItem (OR逻辑 - 多选并行)</h2>
      <p>用户可以同时选择多种资质类型，每种资质独立管理，支持条件验证</p>
      
      <div class="example-demo">
        <QualificationOrItem
          v-model="orQualificationData"
          :elements="qualificationElements"
          :readonly="false"
          :disabled="false"
          name="orQualificationMap"
        />
      </div>
      
      <div class="example-data">
        <h3>当前数据:</h3>
        <pre>{{ JSON.stringify(orQualificationData, null, 2) }}</pre>
      </div>
    </section>

    <!-- 示例3: QualificationImage (简化版) -->
    <section class="example-section">
      <h2>示例3: QualificationImage (简化版 - 纯图片功能)</h2>
      <p>专注于图片上传和预览功能，移除了业务逻辑</p>
      
      <div class="example-demo">
        <QualificationImage
          v-model="imageData"
          title="资质图片"
          description="图片格式支持PNG/JPG/JPEG格式，尺寸800*800px以上，大小5M以内，每个资质最多上传5张"
          :max-count="5"
          :max-size="5"
          :read-only="false"
          :disabled="false"
          @upload="handleImageUpload"
          @delete="handleImageDelete"
          @preview="handleImagePreview"
        />
      </div>
      
      <div class="example-data">
        <h3>当前图片数据:</h3>
        <pre>{{ JSON.stringify(imageData, null, 2) }}</pre>
      </div>
    </section>

    <!-- 示例4: 只读模式对比 -->
    <section class="example-section">
      <h2>示例4: 只读模式对比</h2>
      <p>展示各组件在只读模式下的显示效果</p>
      
      <div class="readonly-examples">
        <div class="readonly-item">
          <h3>QualificationAndItem (只读)</h3>
          <QualificationAndItem
            v-model="readonlyAndData"
            :elements="qualificationElements"
            :readonly="true"
          />
        </div>
        
        <div class="readonly-item">
          <h3>QualificationOrItem (只读)</h3>
          <QualificationOrItem
            v-model="readonlyOrData"
            :elements="qualificationElements"
            :readonly="true"
          />
        </div>
        
        <div class="readonly-item">
          <h3>QualificationImage (只读)</h3>
          <QualificationImage
            v-model="readonlyImageData"
            title="资质图片"
            :read-only="true"
          />
        </div>
      </div>
    </section>

    <!-- 示例5: 验证功能演示 -->
    <section class="example-section">
      <h2>示例5: 验证功能演示</h2>
      <p>展示各组件的验证功能和错误处理</p>
      
      <div class="validation-demo">
        <button @click="validateAndComponents">验证 AND 组件</button>
        <button @click="validateOrComponents">验证 OR 组件</button>
        <button @click="validateImageComponent">验证图片组件</button>
        <button @click="clearAllValidation">清除所有验证</button>
      </div>
      
      <div v-if="validationResults.length > 0" class="validation-results">
        <h3>验证结果:</h3>
        <ul>
          <li v-for="result in validationResults" :key="result.component" :class="result.valid ? 'valid' : 'invalid'">
            {{ result.component }}: {{ result.valid ? '✅ 验证通过' : '❌ 验证失败' }}
            <span v-if="!result.valid"> - {{ result.message }}</span>
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import QualificationAndItem from '../components/H5/QualificationAndItem.vue'
import QualificationOrItem from '../components/H5/QualificationOrItem.vue'
import QualificationImage from '../../QualificationImage/components/H5/BusinessLicenseImage.vue'

// 模拟资质配置数据
const qualificationElements = [
  {
    qualificationConfig: {
      qualificationCode: 'FOOD_LICENSE',
      qualificationName: '食品经营许可证',
      qualificationDesc: '用于食品经营的许可证'
    }
  },
  {
    qualificationConfig: {
      qualificationCode: 'BUSINESS_LICENSE',
      qualificationName: '营业执照',
      qualificationDesc: '企业营业执照'
    }
  },
  {
    qualificationConfig: {
      qualificationCode: 'HEALTH_LICENSE',
      qualificationName: '卫生许可证',
      qualificationDesc: '卫生部门颁发的许可证'
    }
  }
]

// AND 逻辑数据 (单选)
const andQualificationData = ref({})

// OR 逻辑数据 (多选)
const orQualificationData = ref({})

// 纯图片数据
const imageData = ref([])

// 只读模式数据
const readonlyAndData = ref({
  'FOOD_LICENSE': {
    qualificationCode: 'FOOD_LICENSE',
    qualificationName: '食品经营许可证',
    mediaInfoList: [
      {
        name: 'food-license.jpg',
        uid: '1',
        size: 1024000,
        status: 'success',
        width: 800,
        height: 600,
        url: 'https://example.com/food-license.jpg'
      }
    ],
    qualificationValidity: {
      qualValidityPeriod: 1,
      startTime: Date.now(),
      endTime: Date.now() + 365 * 24 * 60 * 60 * 1000
    }
  }
})

const readonlyOrData = ref({
  'FOOD_LICENSE': {
    qualificationCode: 'FOOD_LICENSE',
    qualificationName: '食品经营许可证',
    mediaInfoList: [
      {
        name: 'food-license.jpg',
        uid: '1',
        size: 1024000,
        status: 'success',
        width: 800,
        height: 600,
        url: 'https://example.com/food-license.jpg'
      }
    ],
    qualificationValidity: {
      qualValidityPeriod: 0,
      startTime: 0,
      endTime: 0
    }
  },
  'BUSINESS_LICENSE': {
    qualificationCode: 'BUSINESS_LICENSE',
    qualificationName: '营业执照',
    mediaInfoList: [
      {
        name: 'business-license.jpg',
        uid: '2',
        size: 2048000,
        status: 'success',
        width: 1000,
        height: 800,
        url: 'https://example.com/business-license.jpg'
      }
    ],
    qualificationValidity: {
      qualValidityPeriod: 1,
      startTime: Date.now(),
      endTime: Date.now() + 730 * 24 * 60 * 60 * 1000
    }
  }
})

const readonlyImageData = ref([
  {
    url: 'https://example.com/sample1.jpg',
    uid: '1',
    width: 800,
    height: 600
  },
  {
    url: 'https://example.com/sample2.jpg',
    uid: '2',
    width: 1000,
    height: 800
  }
])

// 验证结果
const validationResults = ref([])

// 组件引用
const andComponentRef = ref()
const orComponentRef = ref()
const imageComponentRef = ref()

// 事件处理
const handleImageUpload = (file: File) => {
  console.log('图片上传:', file.name)
}

const handleImageDelete = (image: any, index: number) => {
  console.log('图片删除:', image, index)
}

const handleImagePreview = (image: any) => {
  console.log('图片预览:', image)
}

// 验证方法
const validateAndComponents = () => {
  // 这里应该调用组件的验证方法
  validationResults.value = [
    { component: 'QualificationAndItem', valid: true, message: '' }
  ]
}

const validateOrComponents = () => {
  validationResults.value = [
    { component: 'QualificationOrItem', valid: true, message: '' }
  ]
}

const validateImageComponent = () => {
  validationResults.value = [
    { component: 'QualificationImage', valid: true, message: '' }
  ]
}

const clearAllValidation = () => {
  validationResults.value = []
}
</script>

<style scoped lang="stylus">
.usage-examples
  padding 20px
  max-width 1200px
  margin 0 auto

.example-section
  margin-bottom 40px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px

.example-section h2
  color #333
  margin-bottom 10px

.example-section p
  color #666
  margin-bottom 20px

.example-demo
  margin-bottom 20px
  padding 20px
  background #f9f9f9
  border-radius 6px

.example-data
  background #f5f5f5
  padding 15px
  border-radius 6px

.example-data h3
  margin-bottom 10px
  color #333

.example-data pre
  background white
  padding 10px
  border-radius 4px
  overflow auto
  font-size 12px

.readonly-examples
  display grid
  grid-template-columns repeat(auto-fit, minmax(300px, 1fr))
  gap 20px

.readonly-item
  padding 15px
  border 1px solid #ddd
  border-radius 6px

.readonly-item h3
  margin-bottom 15px
  color #333

.validation-demo
  margin-bottom 20px

.validation-demo button
  margin-right 10px
  margin-bottom 10px
  padding 8px 16px
  background #007bff
  color white
  border none
  border-radius 4px
  cursor pointer

.validation-demo button:hover
  background #0056b3

.validation-results ul
  list-style none
  padding 0

.validation-results li
  padding 8px 12px
  margin-bottom 5px
  border-radius 4px

.validation-results li.valid
  background #d4edda
  color #155724

.validation-results li.invalid
  background #f8d7da
  color #721c24
</style>
