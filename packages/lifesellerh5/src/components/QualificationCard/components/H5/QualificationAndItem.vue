<template>
  <div class="qualification-and-item-wrapper">
    <!-- 资质类型选择区域 -->
    <div class="qualification-type-section">
      <div class="qualification-type-header">
        <span class="qualification-type-label">资质类型</span>
        <span v-if="readonly" class="required-indicator">*</span>
      </div>

      <RadioGroup
        v-if="!readonly"
        :model-value="qualificationCode"
        :options="options"
        :disabled="disabled"
        @update:model-value="changeType"
      />
      <div v-else class="qualification-type-display">
        <Text>{{ qualificationName || '未选择' }}</Text>
      </div>
    </div>

    <!-- 选中资质类型后显示的内容 -->
    <template v-if="qualificationCode">
      <!-- 证明材料区域 -->
      <div class="qualification-materials-section">
        <div class="materials-header">
          <div class="materials-title">资质图片</div>
          <span class="required-indicator">*</span>
        </div>

        <!-- 资质描述信息 -->
        <div v-if="qualificationConfig?.qualificationDesc" class="materials-description">
          <DescriptionLabel :qualification-config="qualificationConfig" />
        </div>

        <div class="materials-tip">
          图片格式支持PNG/JPG/JPEG格式，尺寸800*800px以上，大小5M以内，每个资质最多上传5张
        </div>

        <!-- 图片预览网格（只读模式） -->
        <div v-if="currentImages.length > 0 && readonly" class="image-grid">
          <div
            v-for="(image, index) in currentImages"
            :key="image.uid || index"
            class="image-item"
          >
            <div class="image-preview">
              <Image
                :src="image.url"
                :alt="`资质图片${index + 1}`"
                fit="cover"
                @click="handleImagePreview(image)"
              />
            </div>
          </div>
        </div>

        <!-- 图片上传区域 -->
        <div v-if="!readonly" class="upload-section">
          <Uploader
            v-model="currentImages"
            :is-preview="false"
            :max-count="uploadConfig.maxCount"
            :max-size="uploadConfig.maxSize"
            :prohibit-operation="disabled || false"
            @update:model-value="handleImageListUpdate"
          />
        </div>

        <!-- 图片验证错误提示 -->
        <div v-if="imageValidationError" class="error-message">
          {{ imageValidationError }}
        </div>
      </div>

      <!-- 有效期区域 -->
      <div class="validity-section">
        <div class="validity-header">
          <div class="validity-title">资质有效期</div>
          <span class="required-indicator">*</span>
        </div>

        <ValidityTimePicker
          :model-value="currentValidity"
          :read-only="readonly"
          :disabled="disabled"
          @update:model-value="handleValidityChange"
        />

        <!-- 有效期验证错误提示 -->
        <div v-if="validityValidationError" class="error-message">
          {{ validityValidationError }}
        </div>
      </div>
    </template>

    <!-- 全局错误提示 -->
    <div v-if="globalError" class="error-message global-error">
      {{ globalError }}
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { RadioGroup, Text } from '@xhs/delight'
  import { Image } from '@xhs/reds-h5-next'
  import { IQualificationElement } from '@edith/edith_get_query_qualification_config'

  // 导入组件
  import Uploader from '~/pages/ClaimStorePage/components/ImageUpload/index.vue'
  import ValidityTimePicker from '../../../ValidityTimePicker/index.vue'
  import DescriptionLabel from './DescriptionLabel.vue'

  // 导入类型和组合式函数
  import { QualificationItem, LicenseValidItem, ImageItem, Period } from '../../core/type'

  // 导入验证器
  import { imagesValidator } from '../../../QualificationImage/core/validator'

  // Props 定义
  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      disabled?: boolean
      readonly?: boolean
      name?: string
      elements: IQualificationElement[]
    }>(),
    {
      name: 'qualificationMap',
      disabled: false,
      readonly: false
    }
  )

  // Events 定义
  const emit = defineEmits(['update:modelValue'])

  // 响应式状态
  const value = ref(props.modelValue || {})
  const imageValidationError = ref('')
  const validityValidationError = ref('')
  const globalError = ref('')

  // 上传配置
  const uploadConfig = {
    maxCount: 5,
    maxSize: 5 // 5MB
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    val => {
      value.value = val || {}
    }
  )

  // 触发变更事件
  const change = () => {
    emit('update:modelValue', value.value)
  }

  // 计算属性 - 资质类型选项
  const options = computed(() => props.elements.map(item => ({
    label: item.qualificationConfig?.qualificationName,
    value: item.qualificationConfig?.qualificationCode
  })))

  // 计算属性 - 当前选中的资质类型（AND逻辑：单选）
  const qualificationCode = computed(() => {
    const keys = props.elements.map(t => t.qualificationConfig?.qualificationCode as string)
    return keys.find(key => key in value.value) || keys[0]
  })

  // 计算属性 - 当前资质配置
  const qualificationConfig = computed(() => {
    const element = props.elements.find(e => e.qualificationConfig?.qualificationCode === qualificationCode.value)
    return element?.qualificationConfig || {}
  })

  // 计算属性 - 资质名称
  const qualificationName = computed(() => qualificationConfig.value?.qualificationName || '')

  // 计算属性 - 当前图片列表
  const currentImages = computed({
    get: () => {
      if (!qualificationCode.value) return []
      return value.value[qualificationCode.value]?.mediaInfoList || []
    },
    set: (newImages: ImageItem[]) => {
      if (!qualificationCode.value) return
      updateQualificationData(qualificationCode.value, { mediaInfoList: newImages })
    }
  })

  // 计算属性 - 当前有效期
  const currentValidity = computed({
    get: () => {
      if (!qualificationCode.value) return null
      const validity = value.value[qualificationCode.value]?.qualificationValidity
      if (!validity) return null

      // 转换为 ValidityTimePickerValue 格式
      return {
        qualValidityPeriod: validity.qualValidityPeriod,
        startTime: validity.startTime,
        endTime: validity.endTime
      }
    },
    set: newValidity => {
      if (!qualificationCode.value) return

      // 转换为 LicenseValidItem 格式
      const licenseValid: LicenseValidItem = newValidity ? {
        qualValidityPeriod: newValidity.qualValidityPeriod,
        startTime: newValidity.startTime || 0,
        endTime: typeof newValidity.endTime === 'string'
          ? new Date(newValidity.endTime).getTime()
          : (newValidity.endTime || 0)
      } : {
        qualValidityPeriod: Period.PERMANENT,
        startTime: 0,
        endTime: 0
      }

      updateQualificationData(qualificationCode.value, { qualificationValidity: licenseValid })
    }
  })

  // 更新资质数据的通用方法
  const updateQualificationData = (typeCode: string, updates: Partial<QualificationItem>) => {
    if (!typeCode) return

    // 确保资质项存在
    if (!value.value[typeCode]) {
      value.value[typeCode] = {
        qualificationCode: typeCode,
        qualificationName: qualificationName.value,
        mediaInfoList: [],
        qualificationValidity: {
          qualValidityPeriod: Period.PERMANENT,
          startTime: 0,
          endTime: 0
        }
      }
    }

    // 更新数据
    value.value[typeCode] = {
      ...value.value[typeCode],
      ...updates
    }

    change()
  }

  // 修改资质类型（AND逻辑：单选互斥）
  const changeType = (val: string) => {
    if (!val) return

    // 保存当前选中资质的数据（用于数据迁移）
    const currentData = qualificationCode.value ? value.value[qualificationCode.value] : {} as QualificationItem

    // 清除旧的资质数据（单选互斥）
    if (qualificationCode.value && qualificationCode.value !== val) {
      delete value.value[qualificationCode.value]
    }

    // 查找新资质的配置信息
    const newQualificationConfig = props.elements.find(e => e.qualificationConfig?.qualificationCode === val)?.qualificationConfig

    // 创建新的资质数据（保留部分旧数据）
    const newData: QualificationItem = {
      qualificationCode: val,
      qualificationName: newQualificationConfig?.qualificationName || '',
      // 保留图片和有效期数据（如果存在）
      mediaInfoList: currentData.mediaInfoList || [],
      qualificationValidity: currentData.qualificationValidity || {
        qualValidityPeriod: Period.PERMANENT,
        startTime: 0,
        endTime: 0
      }
    }

    // 更新数据
    value.value = {
      ...value.value,
      [val]: newData
    }

    change()
  }

  // 图片预览处理
  const handleImagePreview = (_image: ImageItem) => {
    // 这里可以添加图片预览逻辑，比如打开图片查看器
  }

  // 处理图片列表更新
  const handleImageListUpdate = (newImages: any[]) => {
    // 清除之前的错误
    imageValidationError.value = ''

    // 将上传组件的数据格式转换为我们的数据格式
    const formattedImages: ImageItem[] = newImages.map((item, index) => ({
      name: item.name || `image_${index}`,
      uid: item.uid || `${Date.now()}_${index}`,
      size: item.size || 0,
      status: item.status || 'success',
      width: item.width || 0,
      height: item.height || 0,
      url: item.url || item.link || '',
      path: item.path
    }))

    // 验证图片
    if (!validateImages(formattedImages)) {
      return
    }

    // 更新图片数据
    currentImages.value = formattedImages
  }

  // 处理有效期变化
  const handleValidityChange = (newValidity: any) => {
    // 清除之前的错误
    validityValidationError.value = ''

    // 验证有效期
    if (!validateValidity(newValidity)) {
      return
    }

    // 更新有效期数据
    currentValidity.value = newValidity
  }

  // 验证图片
  const validateImages = (imagesToValidate: ImageItem[] = []) => {
    imageValidationError.value = ''

    if (props.readonly || props.disabled) {
      return true
    }

    try {
      // 转换数据格式以适配validator
      const formattedImages = imagesToValidate.map(img => ({
        link: img.path || img.url || '',
        width: img.width || 0,
        height: img.height || 0,
        path: img.path,
        url: img.url
      }))

      const result = imagesValidator(formattedImages, !props.readonly && !props.disabled)

      if (result instanceof Error) {
        imageValidationError.value = result.message
        return false
      }

      return true
    } catch (error) {
      imageValidationError.value = '图片校验失败'
      return false
    }
  }

  // 验证有效期
  const validateValidity = (validity: any) => {
    validityValidationError.value = ''

    if (props.readonly || props.disabled) {
      return true
    }

    if (!validity) {
      validityValidationError.value = '请设置有效期'
      return false
    }

    // 验证期限有效的情况
    if (validity.qualValidityPeriod === Period.NON_PERMANENT) {
      if (!validity.endTime) {
        validityValidationError.value = '请设置有效期结束时间'
        return false
      }

      const endTime = typeof validity.endTime === 'string'
        ? new Date(validity.endTime).getTime()
        : validity.endTime

      if (endTime <= Date.now()) {
        validityValidationError.value = '有效期不能早于当前时间'
        return false
      }

      // 检查30天限制
      const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000)
      if (endTime <= thirtyDaysFromNow) {
        validityValidationError.value = '有效期剩余时间应大于30天'
        return false
      }
    }

    return true
  }

</script>

<style scoped lang="stylus">
.qualification-and-item-wrapper
  padding 16px
  background var(--bg)
  border-radius 12px
  margin-bottom 16px

.qualification-type-section
  margin-bottom 24px

.qualification-type-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 12px

.qualification-type-label
  font-weight 500
  font-size 16px
  color var(--title)

.required-indicator
  color #f56565
  font-size 16px

.qualification-type-display
  padding 8px 0
  color var(--paragraph)

.qualification-materials-section
  margin-bottom 24px

.materials-header
  display flex
  align-items center
  gap 4px
  margin-bottom 8px

.materials-title
  font-weight 500
  font-size 16px
  color var(--title)

.materials-description
  margin-bottom 12px

.materials-tip
  font-size 12px
  color var(--Light-Labels-Description, rgba(0, 0, 0, 0.45))
  margin-bottom 16px

.image-grid
  display grid
  grid-template-columns repeat(3, 1fr)
  gap 8px
  margin-bottom 16px

.image-item
  position relative
  aspect-ratio 1
  border-radius 8px
  overflow hidden

.image-preview
  position relative
  width 100%
  height 100%
  border 1px solid #e0e0e0
  border-radius 8px
  overflow hidden

.upload-section
  margin-bottom 16px

.validity-section
  margin-bottom 16px

.validity-header
  display flex
  align-items center
  gap 4px
  margin-bottom 12px

.validity-title
  font-weight 500
  font-size 16px
  color var(--title)

.error-message
  padding 8px 12px
  color #f56565
  background-color rgba(245, 101, 101, 0.1)
  border-radius 6px
  margin-top 8px
  font-size 14px

.global-error
  margin-top 16px
</style>
