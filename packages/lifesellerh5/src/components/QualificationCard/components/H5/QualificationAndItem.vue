<template>
  <div class="gray-card">
    <!-- 资质类型 -->
    <FormItem label="资质类型" required>
      <RadioGroup
        v-if="!readonly"
        :model-value="qualificationCode"
        :options="options"
        :disabled="disabled"
        @update:model-value="changeType"
      />
      <Text v-else>{{ qualificationName }}</Text>
    </FormItem>

    <template v-if="qualificationCode">
      <!-- 资质图片 -->
      <QualificationImage
        v-if="!(readonly && !value[qualificationCode]?.mediaInfoList?.length)"
        :name="`${name}.${qualificationCode}.mediaInfoList`"
        :model-value="value[qualificationCode]?.mediaInfoList || []"
        :decorator-props="readonly ? { required: true, rules: imageValidator, description: '' } : { required: true, rules: imageValidator }"
        :component-props="{disabled: disabled || readonly}"
        @update:model-value="changeImage"
      >
        <template v-if="qualificationConfig && qualificationConfig.qualificationDesc || qualificationConfig.qualificationImageList?.length" #label>
          <DescriptionLabel :qualification-config="qualificationConfig" />
        </template>
      </QualificationImage>

      <!-- 资质有效期 -->
      <LicenseValidRange
        title="资质有效期"
        :name="`${name}.${qualificationCode}.qualificationValidity`"
        :model-value="value[qualificationCode]?.qualificationValidity || []"
        :decorator-props="{required: true, rules: licenseValidValidRange }"
        :component-props="{disabled, readonly}"
        @update:model-value="changeValidity"
      />
    </template>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { RadioGroup, FormItem2 as FormItem, Text } from '@xhs/delight'
  import { IQualificationElement } from '@edith/edith_get_query_qualification_config'

  import { ImageItem } from '~/types/media'
  import { QualificationItem, LicenseValidItem } from '../../core/type'
  import { LicenseValidRange } from '../../../LicenseValidRange'
  import { QualificationImage } from '../../../QualificationImage'
  import { validator as licenseValidValidRange } from '../../../LicenseValidRange/core/validator'
  import { validator as imageValidator } from '../../../QualificationImage/core/validator'
  import DescriptionLabel from './DescriptionLabel.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      disabled?: boolean
      readonly?: boolean
      name?: string
      elements: IQualificationElement[]
    }>(),
    {
      name: 'qualificationMap'
    }
  )

  const emit = defineEmits(['update:modelValue'])

  const value = ref(props.modelValue || {})

  watch(
    () => props.modelValue,
    val => {
      value.value = val || {}
    }
  )

  const change = () => {
    emit('update:modelValue', value.value)
  }

  const options = computed(() => props.elements.map(item => ({
    label: item.qualificationConfig?.qualificationName,
    value: item.qualificationConfig?.qualificationCode
  })))

  const qualificationCode = computed(() => {
    const keys = props.elements.map(t => t.qualificationConfig?.qualificationCode as string)
    return keys.find(key => key in value.value) || keys[0]
  })
  const qualificationConfig = computed(() => {
    const element = props.elements.find(e => e.qualificationConfig?.qualificationCode === qualificationCode.value)
    return element?.qualificationConfig || {}
  })
  const qualificationName = computed(() => qualificationConfig.value?.qualificationName || '')

  // 修改资质类型
  const changeType = (val: string) => {
    const itemData = qualificationCode.value ? value.value[qualificationCode.value] : {} as QualificationItem
    if (qualificationCode.value) {
      delete value.value[qualificationCode.value]
    }
    const newData: QualificationItem = {
      ...itemData,
      qualificationCode: val,
      qualificationName: props.elements.find(e => e.qualificationConfig?.qualificationCode === val)?.qualificationConfig?.qualificationName || '',
    }
    value.value = {
      ...value.value,
      [val]: newData
    }
    change()
  }

  // 修改图片
  const changeImage = (val: ImageItem[]) => {
    if (!qualificationCode.value) return
    value.value = {
      ...value.value,
      [qualificationCode.value]: {
        ...value.value[qualificationCode.value],
        qualificationName: qualificationName.value,
        qualificationCode: qualificationCode.value,
        mediaInfoList: val
      }
    }
    change()
  }

  // 修改有效期
  const changeValidity = (val: LicenseValidItem) => {
    if (!qualificationCode.value) return
    value.value = {
      ...value.value,
      [qualificationCode.value]: {
        ...value.value[qualificationCode.value],
        qualificationName: qualificationName.value,
        qualificationCode: qualificationCode.value,
        qualificationValidity: val
      }
    }
    change()
  }

</script>
