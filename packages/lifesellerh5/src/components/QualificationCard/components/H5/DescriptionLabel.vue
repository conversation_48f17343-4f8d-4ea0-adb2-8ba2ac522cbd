<template>
  <div>
    <div class="right-title">
      <Text color="text-title" bold>资质图片</Text>
    </div>
    <div
      v-if="qualificationConfig.qualificationDesc || qualificationConfig.qualificationImageList?.length"
    >
      <Popconfirm
        title="材料说明"
        placement="right"
        :size="500"
        arrow
        :with-footer="false"
      >
        <template #description>
          <div
            v-if="qualificationConfig.qualificationImageList?.length"
            class="item"
            style="margin-bottom: 20px;"
          >
            <Text block color="text-title" style="margin-bottom: 6px;">资质示例</Text>
            <Upload
              readonly
              :custom-upload="() => ({})"
              :model-value="images"
              :limit="qualificationConfig.qualificationImageList.length"
            />
          </div>
          <div
            v-if="qualificationConfig?.qualificationDesc"
            class="item"
          >
            <Text color="text-title" block>注意事项</Text>
            <div style="line-height: 1.5">
              <p
                v-for="(item, index) in qualificationConfig.qualificationDesc.split('\n')"
                :key="index"
              >
                <Text>{{ item }}</Text>
              </p>
            </div>
          </div>
        </template>
        <Link>材料说明</Link>
      </Popconfirm>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { computed } from 'vue'
  import { Text, Link, Popconfirm } from '@xhs/delight'
  import Upload, { UploadFile } from '@xhs/delight-material-ultra-enhance-upload'

  import { IQualificationConfig } from '@edith/edith_get_query_qualification_config'

  const props = defineProps<{
    qualificationConfig: IQualificationConfig
  }>()

  const images = computed(() => props.qualificationConfig.qualificationImageList?.map(item => ({
    url: item,
    name: item,
  })) as UploadFile[])

</script>

<style>
div[viewimgs="true"] {
  position: relative;
  z-index: 1100
}
</style>
