<template>
  <div class="qualification-or-item-wrapper">
    <!-- 每个资质类型独立的卡片 -->
    <div
      v-for="item in elements"
      :key="item.qualificationConfig?.qualificationCode"
      class="qualification-card"
    >
      <!-- 资质类型显示区域 -->
      <div class="qualification-type-section">
        <div class="qualification-type-header">
          <span class="qualification-type-label">资质类型</span>
          <span v-if="hasImages(item.qualificationConfig?.qualificationCode)" class="optional-indicator">可选</span>
        </div>

        <div class="qualification-type-display">
          <RadioGroup
            v-if="!readonly"
            :disabled="disabled"
            :model-value="item.qualificationConfig?.qualificationCode"
            :options="[{
              label: item.qualificationConfig?.qualificationName,
              value: item.qualificationConfig?.qualificationCode
            }]"
            readonly
          />
          <Text v-else>{{ item.qualificationConfig?.qualificationName || '-' }}</Text>
        </div>
      </div>

      <!-- 证明材料区域 -->
      <div class="qualification-materials-section">
        <div class="materials-header">
          <div class="materials-title">资质图片</div>
          <span v-if="hasValidity(item.qualificationConfig?.qualificationCode)" class="conditional-required">
            有有效期时必填
          </span>
        </div>

        <!-- 资质描述信息 -->
        <div v-if="item.qualificationConfig?.qualificationDesc" class="materials-description">
          <DescriptionLabel :qualification-config="item.qualificationConfig" />
        </div>

        <div class="materials-tip">
          图片格式支持PNG/JPG/JPEG格式，尺寸800*800px以上，大小5M以内，每个资质最多上传5张
        </div>

        <!-- 图片预览网格（只读模式） -->
        <div
          v-if="getImages(item.qualificationConfig?.qualificationCode).length > 0 && readonly"
          class="image-grid"
        >
          <div
            v-for="(image, index) in getImages(item.qualificationConfig?.qualificationCode)"
            :key="image.uid || index"
            class="image-item"
          >
            <div class="image-preview">
              <Image
                :src="image.url"
                :alt="`资质图片${index + 1}`"
                fit="cover"
                @click="handleImagePreview(image)"
              />
            </div>
          </div>
        </div>

        <!-- 图片上传区域 -->
        <div v-if="!readonly" class="upload-section">
          <Uploader
            :model-value="getImages(item.qualificationConfig?.qualificationCode)"
            :is-preview="false"
            :max-count="uploadConfig.maxCount"
            :max-size="uploadConfig.maxSize"
            :prohibit-operation="disabled || false"
            @update:model-value="(newImages) => handleImageListUpdate(newImages, item.qualificationConfig)"
          />
        </div>

        <!-- 图片验证错误提示 -->
        <div v-if="getImageError(item.qualificationConfig?.qualificationCode)" class="error-message">
          {{ getImageError(item.qualificationConfig?.qualificationCode) }}
        </div>
      </div>

      <!-- 有效期区域 -->
      <div class="validity-section">
        <div class="validity-header">
          <div class="validity-title">资质有效期</div>
          <span v-if="hasImages(item.qualificationConfig?.qualificationCode)" class="conditional-required">
            有图片时必填
          </span>
        </div>

        <ValidityTimePicker
          :model-value="getValidity(item.qualificationConfig?.qualificationCode)"
          :read-only="readonly"
          :disabled="disabled"
          @update:model-value="(newValidity) => handleValidityChange(newValidity, item.qualificationConfig)"
        />

        <!-- 有效期验证错误提示 -->
        <div v-if="getValidityError(item.qualificationConfig?.qualificationCode)" class="error-message">
          {{ getValidityError(item.qualificationConfig?.qualificationCode) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch } from 'vue'
  import { RadioGroup, Text } from '@xhs/delight'
  import { Image } from '@xhs/reds-h5-next'
  import { IQualificationElement, IQualificationConfig } from '@edith/edith_get_query_qualification_config'

  // 导入组件
  import Uploader from '~/pages/ClaimStorePage/components/ImageUpload/index.vue'
  import ValidityTimePicker from '../../../ValidityTimePicker/index.vue'
  import DescriptionLabel from './DescriptionLabel.vue'

  // 导入类型
  import { QualificationItem, LicenseValidItem, ImageItem, Period } from '../../core/type'

  // 导入验证器
  import { imagesValidator } from '../../../QualificationImage/core/validator'

  // Props 定义
  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      disabled?: boolean
      readonly?: boolean
      name?: string
      elements: IQualificationElement[]
    }>(),
    {
      name: 'qualificationMap',
      disabled: false,
      readonly: false
    }
  )

  // Events 定义
  const emit = defineEmits(['update:modelValue'])

  // 响应式状态
  const value = ref(props.modelValue || {})
  const imageErrors = ref<Record<string, string>>({})
  const validityErrors = ref<Record<string, string>>({})

  // 上传配置
  const uploadConfig = {
    maxCount: 5,
    maxSize: 5 // 5MB
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    val => {
      value.value = val || {}
    }
  )

  // 触发变更事件
  const change = () => {
    emit('update:modelValue', value.value)
  }

  // 更新资质数据的通用方法
  const updateQualificationData = (typeCode: string, updates: Partial<QualificationItem>) => {
    if (!typeCode) return

    // 确保资质项存在
    if (!value.value[typeCode]) {
      const config = props.elements.find(e => e.qualificationConfig?.qualificationCode === typeCode)?.qualificationConfig
      value.value[typeCode] = {
        qualificationCode: typeCode,
        qualificationName: config?.qualificationName || '',
        mediaInfoList: [],
        qualificationValidity: {
          qualValidityPeriod: Period.PERMANENT,
          startTime: 0,
          endTime: 0
        }
      }
    }

    // 更新数据
    value.value[typeCode] = {
      ...value.value[typeCode],
      ...updates
    }

    change()
  }

  // 获取指定资质类型的图片列表
  const getImages = (typeCode?: string): ImageItem[] => {
    if (!typeCode) return []
    return value.value[typeCode]?.mediaInfoList || []
  }

  // 获取指定资质类型的有效期
  const getValidity = (typeCode?: string) => {
    if (!typeCode) return null
    const validity = value.value[typeCode]?.qualificationValidity
    if (!validity) return null

    // 转换为 ValidityTimePickerValue 格式
    return {
      qualValidityPeriod: validity.qualValidityPeriod,
      startTime: validity.startTime,
      endTime: validity.endTime
    }
  }

  // 检查是否有图片
  const hasImages = (typeCode?: string): boolean => {
    if (!typeCode) return false
    return getImages(typeCode).length > 0
  }

  // 检查是否有有效期
  const hasValidity = (typeCode?: string): boolean => {
    if (!typeCode) return false
    const validity = value.value[typeCode]?.qualificationValidity
    if (!validity) return false

    if (validity.qualValidityPeriod === Period.PERMANENT) return true
    return validity.qualValidityPeriod === Period.NON_PERMANENT && !!validity.endTime
  }

  // 获取图片验证错误
  const getImageError = (typeCode?: string): string => {
    if (!typeCode) return ''
    return imageErrors.value[typeCode] || ''
  }

  // 获取有效期验证错误
  const getValidityError = (typeCode?: string): string => {
    if (!typeCode) return ''
    return validityErrors.value[typeCode] || ''
  }

  // 图片预览处理
  const handleImagePreview = (_image: ImageItem) => {
    // 这里可以添加图片预览逻辑，比如打开图片查看器
  }

  // 处理图片列表更新
  const handleImageListUpdate = (newImages: any[], config?: IQualificationConfig) => {
    if (!config?.qualificationCode) return

    const typeCode = config.qualificationCode

    // 清除之前的错误
    delete imageErrors.value[typeCode]

    // 将上传组件的数据格式转换为我们的数据格式
    const formattedImages: ImageItem[] = newImages.map((item, index) => ({
      name: item.name || `image_${index}`,
      uid: item.uid || `${Date.now()}_${index}`,
      size: item.size || 0,
      status: item.status || 'success',
      width: item.width || 0,
      height: item.height || 0,
      url: item.url || item.link || '',
      path: item.path
    }))

    // 验证图片
    if (!validateImages(formattedImages, typeCode)) {
      return
    }

    // 更新图片数据
    updateQualificationData(typeCode, { mediaInfoList: formattedImages })
  }

  // 处理有效期变化
  const handleValidityChange = (newValidity: any, config?: IQualificationConfig) => {
    if (!config?.qualificationCode) return

    const typeCode = config.qualificationCode

    // 清除之前的错误
    delete validityErrors.value[typeCode]

    // 验证有效期
    if (!validateValidity(newValidity, typeCode)) {
      return
    }

    // 转换为 LicenseValidItem 格式
    const licenseValid: LicenseValidItem = newValidity ? {
      qualValidityPeriod: newValidity.qualValidityPeriod,
      startTime: newValidity.startTime || 0,
      endTime: typeof newValidity.endTime === 'string'
        ? new Date(newValidity.endTime).getTime()
        : (newValidity.endTime || 0)
    } : {
      qualValidityPeriod: Period.PERMANENT,
      startTime: 0,
      endTime: 0
    }

    // 更新有效期数据
    updateQualificationData(typeCode, { qualificationValidity: licenseValid })
  }

  // 验证图片（OR逻辑：条件验证）
  const validateImages = (imagesToValidate: ImageItem[], typeCode: string): boolean => {
    delete imageErrors.value[typeCode]

    if (props.readonly || props.disabled) {
      return true
    }

    // OR逻辑：如果有有效期设置，则图片为必填
    const hasValiditySet = hasValidity(typeCode)

    if (hasValiditySet && imagesToValidate.length === 0) {
      imageErrors.value[typeCode] = '设置有效期后必须上传图片'
      return false
    }

    try {
      // 转换数据格式以适配validator
      const formattedImages = imagesToValidate.map(img => ({
        link: img.path || img.url || '',
        width: img.width || 0,
        height: img.height || 0,
        path: img.path,
        url: img.url
      }))

      const result = imagesValidator(formattedImages, hasValiditySet)

      if (result instanceof Error) {
        imageErrors.value[typeCode] = result.message
        return false
      }

      return true
    } catch (error) {
      imageErrors.value[typeCode] = '图片校验失败'
      return false
    }
  }

  // 验证有效期（OR逻辑：条件验证）
  const validateValidity = (validity: any, typeCode: string): boolean => {
    delete validityErrors.value[typeCode]

    if (props.readonly || props.disabled) {
      return true
    }

    // OR逻辑：如果有图片，则有效期为必填
    const hasImagesSet = hasImages(typeCode)

    if (hasImagesSet && !validity) {
      validityErrors.value[typeCode] = '上传图片后必须设置有效期'
      return false
    }

    if (validity) {
      // 验证期限有效的情况
      if (validity.qualValidityPeriod === Period.NON_PERMANENT) {
        if (!validity.endTime) {
          validityErrors.value[typeCode] = '请设置有效期结束时间'
          return false
        }

        const endTime = typeof validity.endTime === 'string'
          ? new Date(validity.endTime).getTime()
          : validity.endTime

        if (endTime <= Date.now()) {
          validityErrors.value[typeCode] = '有效期不能早于当前时间'
          return false
        }

        // 检查30天限制
        const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000)
        if (endTime <= thirtyDaysFromNow) {
          validityErrors.value[typeCode] = '有效期剩余时间应大于30天'
          return false
        }
      }
    }

    return true
  }
</script>

<style scoped lang="stylus">
.qualification-or-item-wrapper
  display flex
  flex-direction column
  gap 16px

.qualification-card
  padding 16px
  background var(--bg)
  border-radius 12px
  border 1px solid #e0e0e0

.qualification-type-section
  margin-bottom 20px

.qualification-type-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 12px

.qualification-type-label
  font-weight 500
  font-size 16px
  color var(--title)

.optional-indicator
  color #52c41a
  font-size 12px
  background-color rgba(82, 196, 26, 0.1)
  padding 2px 8px
  border-radius 4px

.qualification-type-display
  padding 8px 0
  color var(--paragraph)

.qualification-materials-section
  margin-bottom 20px

.materials-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 8px

.materials-title
  font-weight 500
  font-size 16px
  color var(--title)

.conditional-required
  color #fa8c16
  font-size 12px
  background-color rgba(250, 140, 22, 0.1)
  padding 2px 8px
  border-radius 4px

.materials-description
  margin-bottom 12px

.materials-tip
  font-size 12px
  color var(--Light-Labels-Description, rgba(0, 0, 0, 0.45))
  margin-bottom 16px

.image-grid
  display grid
  grid-template-columns repeat(3, 1fr)
  gap 8px
  margin-bottom 16px

.image-item
  position relative
  aspect-ratio 1
  border-radius 8px
  overflow hidden

.image-preview
  position relative
  width 100%
  height 100%
  border 1px solid #e0e0e0
  border-radius 8px
  overflow hidden

.upload-section
  margin-bottom 16px

.validity-section
  margin-bottom 16px

.validity-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 12px

.validity-title
  font-weight 500
  font-size 16px
  color var(--title)

.error-message
  padding 8px 12px
  color #f56565
  background-color rgba(245, 101, 101, 0.1)
  border-radius 6px
  margin-top 8px
  font-size 14px
</style>
