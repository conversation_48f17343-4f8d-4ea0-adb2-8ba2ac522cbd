<template>
  <div v-for="item in elements" :key="item.qualificationConfig?.qualificationCode" class="gray-card">
    <!-- 资质类型 -->
    <FormItem label="资质类型">
      <RadioGroup
        v-if="!readonly"
        :disabled="disabled"
        :model-value="item.qualificationConfig?.qualificationCode"
        :options="[{label: item.qualificationConfig?.qualificationName, value: item.qualificationConfig?.qualificationCode}]"
      />
      <Text v-else>{{ item.qualificationConfig?.qualificationName || '-' }}</Text>
    </FormItem>

    <!-- 资质图片 -->
    <QualificationImage
      :name="`${name}.${item.qualificationConfig?.qualificationCode}.mediaInfoList`"
      :model-value="value[item.qualificationConfig?.qualificationCode as string]?.mediaInfoList || []"
      :decorator-props="readonly ? { description: '' } : { rules: getImageValidatorByValid(value[item.qualificationConfig?.qualificationCode as string]?.qualificationValidity) }"
      :component-props="{disabled: disabled || readonly}"
      @update:model-value="changeImage($event, item.qualificationConfig)"
    >
      <template v-if="item.qualificationConfig && (item.qualificationConfig.qualificationDesc || item.qualificationConfig.qualificationImageList?.length)" #label>
        <DescriptionLabel :qualification-config="item.qualificationConfig" />
      </template>
    </QualificationImage>

    <!-- 资质有效期 -->
    <LicenseValidRange
      title="资质有效期"
      :required="false"
      :name="`${name}.${item.qualificationConfig?.qualificationCode}.qualificationValidity`"
      :model-value="value[item.qualificationConfig?.qualificationCode as string]?.qualificationValidity"
      :decorator-props="{rules: getValidValidatorByMedia(value[item.qualificationConfig?.qualificationCode as string]?.mediaInfoList), required: false}"
      :component-props="{disabled, readonly}"
      @update:model-value="changeValidity($event, item.qualificationConfig)"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch } from 'vue'
  import { RadioGroup, FormItem2 as FormItem, Text } from '@xhs/delight'
  import { IQualificationElement, IQualificationConfig } from '@edith/edith_get_query_qualification_config'

  import { ImageItem } from '~/types/media'
  import { QualificationItem, LicenseValidItem, Period } from '../../core/type'
  import { LicenseValidRange } from '../../../LicenseValidRange'
  import { getValidator as getValidRangeValidator } from '../../../LicenseValidRange/core/validator'
  import { QualificationImage } from '../../../QualificationImage'
  import { getValidator as getImageValidator } from '../../../QualificationImage/core/validator'
  import DescriptionLabel from './DescriptionLabel.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      disabled?: boolean
      readonly?: boolean
      name?: string
      elements: IQualificationElement[]
    }>(),
    {
      name: 'qualificationMap'
    }
  )

  const emit = defineEmits(['update:modelValue'])

  const value = ref(props.modelValue || {})

  watch(
    () => props.modelValue,
    val => {
      value.value = val || {}
    }
  )

  const change = () => {
    emit('update:modelValue', value.value)
  }

  // 修改图片
  const changeImage = (val: ImageItem[], config?: IQualificationConfig) => {
    if (!config) return
    const qualificationCode = config.qualificationCode as string
    const qualificationName = config.qualificationName as string

    value.value = {
      ...value.value,
      [qualificationCode]: {
        ...value.value[qualificationCode],
        qualificationValidity: value.value[qualificationCode]?.qualificationValidity || {} as LicenseValidItem,
        qualificationCode,
        qualificationName,
        mediaInfoList: val
      }
    }
    change()
  }

  // 修改有效期
  const changeValidity = (val: LicenseValidItem, config?: IQualificationConfig) => {
    if (!config) return
    const qualificationCode = config.qualificationCode as string
    const qualificationName = config.qualificationName as string

    value.value = {
      ...value.value,
      [qualificationCode]: {
        ...value.value[qualificationCode],
        qualificationCode,
        qualificationValidity: val,
        qualificationName
      }
    }
    change()
  }

  // 是否选中有效期
  const hasValid = (value?: LicenseValidItem) => {
    const period = value?.qualValidityPeriod
    if (period === Period.PERMANENT) return true
    return period === Period.NON_PERMANENT && !!value?.endTime
  }

  // 图片校验
  const getImageValidatorByValid = (valid: LicenseValidItem) => getImageValidator(hasValid(valid))

  // 有效期检验
  const getValidValidatorByMedia = (media?: ImageItem[]) => (media?.length ? getValidRangeValidator(true) : [])
</script>

<style lang="stylus" scoped>
.gray-card
  background-color #fafafa
  padding 20px
  border-radius 4px
  display flex
  flex-direction column
  gap: 12px
</style>
