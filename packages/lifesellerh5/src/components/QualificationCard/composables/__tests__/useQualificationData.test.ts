import { describe, it, expect, beforeEach } from 'vitest'
import { useQualificationData } from '../useQualificationData'
import { QualificationSelectionMode } from '../../core/type'

// Mock数据
const mockElements = [
  {
    qualificationConfig: {
      qualificationCode: 'FOOD_LICENSE',
      qualificationName: '食品经营许可证'
    }
  },
  {
    qualificationConfig: {
      qualificationCode: 'BUSINESS_LICENSE',
      qualificationName: '营业执照'
    }
  }
]

describe('useQualificationData', () => {
  describe('单选模式 (AND逻辑)', () => {
    let qualificationData: ReturnType<typeof useQualificationData>

    beforeEach(() => {
      qualificationData = useQualificationData(
        mockElements as any,
        {
          mode: QualificationSelectionMode.SINGLE,
          validateOnChange: false,
          autoSync: false
        }
      )
    })

    it('应该正确初始化', () => {
      expect(qualificationData.hasData.value).toBe(false)
      expect(qualificationData.isDataComplete.value).toBe(true) // 空数据被认为是完整的
    })

    it('应该支持单选资质类型', () => {
      // 选择第一个资质类型
      qualificationData.selectType('FOOD_LICENSE')
      
      expect(qualificationData.selectedType.value).toBe('FOOD_LICENSE')
      expect(qualificationData.hasSelection.value).toBe(true)
      expect(qualificationData.currentData.value['FOOD_LICENSE']).toBeDefined()
    })

    it('应该在切换资质类型时清除旧数据', () => {
      // 选择第一个资质类型
      qualificationData.selectType('FOOD_LICENSE')
      expect(qualificationData.currentData.value['FOOD_LICENSE']).toBeDefined()
      
      // 切换到第二个资质类型
      qualificationData.selectType('BUSINESS_LICENSE')
      expect(qualificationData.currentData.value['FOOD_LICENSE']).toBeUndefined()
      expect(qualificationData.currentData.value['BUSINESS_LICENSE']).toBeDefined()
      expect(qualificationData.selectedType.value).toBe('BUSINESS_LICENSE')
    })

    it('应该正确更新图片数据', () => {
      qualificationData.selectType('FOOD_LICENSE')
      
      const mockImages = [
        { name: 'test', uid: '1', size: 1000, status: 'success' as const, width: 800, height: 600, url: 'test.jpg' }
      ]
      
      qualificationData.updateImages('FOOD_LICENSE', mockImages)
      
      expect(qualificationData.currentData.value['FOOD_LICENSE'].mediaInfoList).toEqual(mockImages)
    })

    it('应该正确更新有效期数据', () => {
      qualificationData.selectType('FOOD_LICENSE')
      
      const mockValidity = {
        qualValidityPeriod: 1,
        startTime: Date.now(),
        endTime: Date.now() + 365 * 24 * 60 * 60 * 1000 // 一年后
      }
      
      qualificationData.updateValidity('FOOD_LICENSE', mockValidity)
      
      expect(qualificationData.currentData.value['FOOD_LICENSE'].qualificationValidity).toEqual(mockValidity)
    })
  })

  describe('多选模式 (OR逻辑)', () => {
    let qualificationData: ReturnType<typeof useQualificationData>

    beforeEach(() => {
      qualificationData = useQualificationData(
        mockElements as any,
        {
          mode: QualificationSelectionMode.MULTIPLE,
          validateOnChange: false,
          autoSync: false
        }
      )
    })

    it('应该支持多选资质类型', () => {
      // 选择多个资质类型
      qualificationData.toggleType('FOOD_LICENSE')
      qualificationData.toggleType('BUSINESS_LICENSE')
      
      expect(qualificationData.selectedTypeList.value).toContain('FOOD_LICENSE')
      expect(qualificationData.selectedTypeList.value).toContain('BUSINESS_LICENSE')
      expect(qualificationData.selectedTypeList.value.length).toBe(2)
    })

    it('应该支持取消选择资质类型', () => {
      // 选择资质类型
      qualificationData.toggleType('FOOD_LICENSE')
      expect(qualificationData.selectedTypeList.value).toContain('FOOD_LICENSE')
      
      // 取消选择
      qualificationData.toggleType('FOOD_LICENSE')
      expect(qualificationData.selectedTypeList.value).not.toContain('FOOD_LICENSE')
      expect(qualificationData.currentData.value['FOOD_LICENSE']).toBeUndefined()
    })
  })

  describe('数据验证', () => {
    let qualificationData: ReturnType<typeof useQualificationData>

    beforeEach(() => {
      qualificationData = useQualificationData(
        mockElements as any,
        {
          mode: QualificationSelectionMode.SINGLE,
          validateOnChange: false,
          autoSync: false
        }
      )
    })

    it('应该验证空数据', async () => {
      const result = await qualificationData.validateAll()
      expect(result.valid).toBe(false)
      expect(result.errors.global).toContain('请选择资质类型')
    })

    it('应该验证不完整的数据', async () => {
      qualificationData.selectType('FOOD_LICENSE')
      
      const result = await qualificationData.validateAll()
      expect(result.valid).toBe(false)
      expect(result.errors['FOOD_LICENSE']).toContain('请上传资质图片')
    })
  })

  describe('数据序列化', () => {
    let qualificationData: ReturnType<typeof useQualificationData>

    beforeEach(() => {
      qualificationData = useQualificationData(
        mockElements as any,
        {
          mode: QualificationSelectionMode.SINGLE,
          validateOnChange: false,
          autoSync: false
        }
      )
    })

    it('应该正确序列化数据', () => {
      qualificationData.selectType('FOOD_LICENSE')
      
      const mockImages = [
        { name: 'test', uid: '1', size: 1000, status: 'success' as const, width: 800, height: 600, url: 'test.jpg' }
      ]
      const mockValidity = {
        qualValidityPeriod: 1,
        startTime: Date.now(),
        endTime: Date.now() + 365 * 24 * 60 * 60 * 1000
      }
      
      qualificationData.updateImages('FOOD_LICENSE', mockImages)
      qualificationData.updateValidity('FOOD_LICENSE', mockValidity)
      
      const serialized = qualificationData.serializeData()
      
      expect(serialized['FOOD_LICENSE']).toBeDefined()
      expect(serialized['FOOD_LICENSE'].qualificationCode).toBe('FOOD_LICENSE')
      expect(serialized['FOOD_LICENSE'].mediaInfoList).toEqual(mockImages)
      expect(serialized['FOOD_LICENSE'].qualificationValidity).toEqual(mockValidity)
    })

    it('应该正确反序列化数据', () => {
      const mockData = {
        'FOOD_LICENSE': {
          qualificationCode: 'FOOD_LICENSE',
          qualificationName: '食品经营许可证',
          mediaInfoList: [
            { name: 'test', uid: '1', size: 1000, status: 'success' as const, width: 800, height: 600, url: 'test.jpg' }
          ],
          qualificationValidity: {
            qualValidityPeriod: 1,
            startTime: Date.now(),
            endTime: Date.now() + 365 * 24 * 60 * 60 * 1000
          }
        }
      }
      
      qualificationData.deserializeData(mockData)
      
      expect(qualificationData.currentData.value).toEqual(mockData)
      expect(qualificationData.selectedTypeList.value).toContain('FOOD_LICENSE')
    })
  })
})
