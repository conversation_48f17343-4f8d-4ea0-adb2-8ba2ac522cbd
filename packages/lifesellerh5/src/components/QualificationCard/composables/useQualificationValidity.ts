import { ref, computed, watch } from 'vue'
import { Period, LicenseValidItem, ImageItem } from '../core/type'

/**
 * 资质有效期管理组合式函数
 * 处理有效期的设置、验证和状态管理
 */
export function useQualificationValidity(
  initialValidity?: LicenseValidItem,
  options: {
    validateOnChange?: boolean
    requireValidityWhenHasImages?: boolean
    requireImagesWhenHasValidity?: boolean
  } = {}
) {
  const {
    validateOnChange = true,
    requireValidityWhenHasImages = true,
    requireImagesWhenHasValidity = true
  } = options

  // 响应式状态
  const validity = ref<LicenseValidItem | null>(initialValidity || null)
  const validationErrors = ref<string[]>([])
  const isValidating = ref(false)

  // 计算属性 - 是否为永久有效
  const isPermanent = computed(() => 
    validity.value?.qualValidityPeriod === Period.PERMANENT
  )

  // 计算属性 - 是否为期限有效
  const isNonPermanent = computed(() => 
    validity.value?.qualValidityPeriod === Period.NON_PERMANENT
  )

  // 计算属性 - 是否有有效的有效期设置
  const hasValidValidity = computed(() => {
    if (!validity.value) return false
    
    if (isPermanent.value) {
      return true
    }
    
    if (isNonPermanent.value) {
      return !!(validity.value.endTime && validity.value.endTime > Date.now())
    }
    
    return false
  })

  // 计算属性 - 有效期显示文本
  const validityDisplayText = computed(() => {
    if (!validity.value) return '未设置'
    
    if (isPermanent.value) {
      return '永久有效'
    }
    
    if (isNonPermanent.value && validity.value.endTime) {
      const endDate = new Date(validity.value.endTime)
      return `有效期至 ${endDate.toLocaleDateString()}`
    }
    
    return '未设置'
  })

  // 计算属性 - 是否即将过期（30天内）
  const isExpiringSoon = computed(() => {
    if (!validity.value || isPermanent.value) return false
    
    if (isNonPermanent.value && validity.value.endTime) {
      const endTime = typeof validity.value.endTime === 'string' 
        ? new Date(validity.value.endTime).getTime()
        : validity.value.endTime
      const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000)
      return endTime <= thirtyDaysFromNow
    }
    
    return false
  })

  // 设置永久有效
  const setPermanent = () => {
    validity.value = {
      qualValidityPeriod: Period.PERMANENT,
      startTime: 0,
      endTime: 0
    }
    
    if (validateOnChange) {
      validateValidity()
    }
  }

  // 设置期限有效
  const setNonPermanent = (startTime?: number, endTime?: number | string) => {
    validity.value = {
      qualValidityPeriod: Period.NON_PERMANENT,
      startTime: startTime || Date.now(),
      endTime: endTime || 0
    }
    
    if (validateOnChange) {
      validateValidity()
    }
  }

  // 更新有效期
  const updateValidity = (newValidity: LicenseValidItem | null) => {
    validity.value = newValidity
    
    if (validateOnChange) {
      validateValidity()
    }
  }

  // 清除有效期
  const clearValidity = () => {
    validity.value = null
    validationErrors.value = []
  }

  // 验证有效期
  const validateValidity = (images?: ImageItem[]): { valid: boolean; errors: string[] } => {
    isValidating.value = true
    const errors: string[] = []
    
    try {
      // 基础有效期验证
      if (!validity.value) {
        if (requireValidityWhenHasImages && images && images.length > 0) {
          errors.push('上传图片后必须设置有效期')
        }
      } else {
        // 验证期限有效的情况
        if (isNonPermanent.value) {
          if (!validity.value.endTime) {
            errors.push('请设置有效期结束时间')
          } else {
            const endTime = typeof validity.value.endTime === 'string' 
              ? new Date(validity.value.endTime).getTime()
              : validity.value.endTime
            
            if (endTime <= Date.now()) {
              errors.push('有效期不能早于当前时间')
            }
            
            // 检查30天限制
            const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000)
            if (endTime <= thirtyDaysFromNow) {
              errors.push('有效期剩余时间应大于30天')
            }
          }
        }
      }
      
      validationErrors.value = errors
      
      return {
        valid: errors.length === 0,
        errors
      }
    } finally {
      isValidating.value = false
    }
  }

  // 验证有效期与图片的关联性
  const validateValidityWithImages = (images: ImageItem[]): { valid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    // 如果有图片但没有有效期
    if (requireValidityWhenHasImages && images.length > 0 && !hasValidValidity.value) {
      errors.push('上传图片后必须设置有效期')
    }
    
    // 如果有有效期但没有图片
    if (requireImagesWhenHasValidity && hasValidValidity.value && images.length === 0) {
      errors.push('设置有效期后必须上传图片')
    }
    
    // 合并基础验证错误
    const basicValidation = validateValidity(images)
    errors.push(...basicValidation.errors)
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 重置有效期
  const reset = () => {
    validity.value = initialValidity || null
    validationErrors.value = []
    isValidating.value = false
  }

  // 获取有效期数据用于提交
  const getValidityForSubmit = (): LicenseValidItem | null => {
    if (!validity.value) return null
    
    return {
      qualValidityPeriod: validity.value.qualValidityPeriod,
      startTime: validity.value.startTime,
      endTime: validity.value.endTime
    }
  }

  // 从外部数据设置有效期
  const setValidityFromData = (data: any) => {
    if (!data) {
      validity.value = null
      return
    }
    
    // 兼容不同的数据格式
    if (typeof data === 'object') {
      validity.value = {
        qualValidityPeriod: data.qualValidityPeriod ?? Period.PERMANENT,
        startTime: data.startTime ?? 0,
        endTime: data.endTime ?? 0
      }
    }
  }

  // 监听有效期变化
  watch(
    () => validity.value,
    (newValidity) => {
      if (validateOnChange && newValidity) {
        // 延迟验证，避免频繁触发
        setTimeout(() => {
          validateValidity()
        }, 100)
      }
    },
    { deep: true }
  )

  return {
    // 响应式状态
    validity,
    validationErrors,
    isValidating,
    
    // 计算属性
    isPermanent,
    isNonPermanent,
    hasValidValidity,
    validityDisplayText,
    isExpiringSoon,
    
    // 方法
    setPermanent,
    setNonPermanent,
    updateValidity,
    clearValidity,
    validateValidity,
    validateValidityWithImages,
    reset,
    getValidityForSubmit,
    setValidityFromData
  }
}
