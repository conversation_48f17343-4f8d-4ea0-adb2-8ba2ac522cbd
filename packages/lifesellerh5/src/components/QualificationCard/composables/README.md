# 资质管理组合式函数

本目录包含了用于资质管理的可复用组合式函数，支持不同的业务场景和逻辑模式。

## 组合式函数概览

### 1. useQualificationTypeSelection
管理资质类型选择逻辑，支持单选（AND逻辑）和多选（OR逻辑）模式。

**主要功能：**
- 资质类型的选择和切换
- 单选模式下的互斥逻辑
- 多选模式下的并行选择
- 资质类型与数据的关联管理

### 2. useQualificationValidity
管理有效期相关逻辑，包括永久有效和期限有效两种模式。

**主要功能：**
- 有效期的设置和验证
- 永久有效 vs 期限有效的逻辑处理
- 有效期与图片的联动验证
- 30天有效期限制检查

### 3. useQualificationData
整合资质类型、图片、有效期的统一数据管理。

**主要功能：**
- 统一的数据状态管理
- 数据的序列化和反序列化
- 多层验证逻辑整合
- 数据完整性检查

## 使用示例

### 基础用法

```typescript
import { useQualificationData } from './composables'
import { QualificationSelectionMode } from './core/type'

// 单选模式（AND逻辑）
const qualificationData = useQualificationData(
  elements, // 资质配置数据
  {
    mode: QualificationSelectionMode.SINGLE,
    validateOnChange: true,
    autoSync: true
  }
)

// 选择资质类型
qualificationData.selectType('FOOD_LICENSE')

// 更新图片
qualificationData.updateImages('FOOD_LICENSE', imageList)

// 更新有效期
qualificationData.updateValidity('FOOD_LICENSE', validityData)

// 验证数据
const validation = await qualificationData.validateAll()
if (validation.valid) {
  // 序列化数据用于提交
  const submitData = qualificationData.serializeData()
}
```

### 在组件中使用

```vue
<script setup lang="ts">
import { useQualificationData } from '../composables'
import { QualificationSelectionMode } from '../core/type'

const props = defineProps<{
  elements: IQualificationElement[]
  modelValue: Record<string, QualificationItem>
}>()

const emit = defineEmits(['update:modelValue'])

// 使用组合式函数
const qualificationData = useQualificationData(
  props.elements,
  {
    mode: QualificationSelectionMode.SINGLE,
    validateOnChange: true
  },
  props.modelValue
)

// 监听数据变化
watch(
  () => qualificationData.currentData.value,
  (newData) => {
    emit('update:modelValue', newData)
  },
  { deep: true }
)
</script>
```

## 组件重构对比

### 重构前 vs 重构后

| 特性 | 重构前 | 重构后 |
|------|--------|--------|
| **代码复用** | 每个组件独立实现逻辑 | 共享组合式函数 |
| **类型安全** | 部分类型检查 | 完整的 TypeScript 支持 |
| **验证逻辑** | 分散在各组件中 | 统一的验证机制 |
| **数据管理** | 手动状态同步 | 自动化数据管理 |
| **测试覆盖** | 难以单独测试逻辑 | 可独立测试组合式函数 |

### 兼容性保证

1. **API 兼容性**：保持原有组件的 Props 和 Events 接口不变
2. **数据结构兼容性**：继续使用现有的 `QualificationItem` 等类型
3. **验证规则兼容性**：保持原有的验证逻辑和错误信息
4. **事件通信兼容性**：保持原有的父子组件通信方式

## 业务逻辑差异

### AND 逻辑（单选模式）
- **使用场景**：用户必须且只能选择一种资质类型
- **数据管理**：选择新类型时清除旧数据，支持数据迁移
- **验证策略**：所有字段都是必填的

### OR 逻辑（多选模式）
- **使用场景**：用户可以同时选择多种资质类型
- **数据管理**：每种资质类型独立管理
- **验证策略**：条件验证，有图片才要求有效期

## 测试

运行测试：
```bash
npm run test packages/lifesellerh5/src/components/QualificationCard/composables/__tests__
```

测试覆盖了：
- 单选和多选模式的基本功能
- 数据验证逻辑
- 数据序列化和反序列化
- 边界情况处理

## 注意事项

1. **性能优化**：组合式函数使用了适当的计算属性和监听器，避免不必要的重新计算
2. **内存管理**：图片 URL 的创建和释放得到了妥善处理
3. **错误处理**：提供了完整的错误信息和验证反馈
4. **扩展性**：设计支持未来的功能扩展和定制化需求
