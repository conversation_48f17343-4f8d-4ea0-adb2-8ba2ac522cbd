import { ref, computed, watch } from 'vue'
import type { IQualificationElement } from '@edith/edith_get_query_qualification_config'
import { QualificationSelectionMode, QualificationTypeConfig, QualificationItem } from '../core/type'

/**
 * 资质类型选择组合式函数
 * 支持单选（AND逻辑）和多选（OR逻辑）两种模式
 */
export function useQualificationTypeSelection(
  elements: IQualificationElement[],
  mode: QualificationSelectionMode = QualificationSelectionMode.SINGLE,
  initialValue: Record<string, QualificationItem> = {}
) {
  // 响应式状态
  const selectedTypes = ref<Set<string>>(new Set())
  const currentValue = ref<Record<string, QualificationItem>>(initialValue)

  // 计算属性 - 资质类型选项
  const options = computed(() =>
    elements.map(item => ({
      label: item.qualificationConfig?.qualificationName || '',
      value: item.qualificationConfig?.qualificationCode || '',
      disabled: false
    }))
  )

  // 计算属性 - 资质类型配置映射
  const typeConfigMap = computed(() => {
    const map = new Map<string, QualificationTypeConfig>()
    elements.forEach(element => {
      const config = element.qualificationConfig
      if (config?.qualificationCode) {
        map.set(config.qualificationCode, {
          qualificationCode: config.qualificationCode,
          qualificationName: config.qualificationName || '',
          required: true,
          disabled: false
        })
      }
    })
    return map
  })

  // 计算属性 - 当前选中的资质类型（单选模式）
  const selectedType = computed(() => {
    if (mode === QualificationSelectionMode.SINGLE) {
      const keys = Array.from(selectedTypes.value)
      return keys[0] || null
    }
    return null
  })

  // 计算属性 - 当前选中的资质类型列表（多选模式）
  const selectedTypeList = computed(() => Array.from(selectedTypes.value))

  // 计算属性 - 是否有选中的资质类型
  const hasSelection = computed(() => selectedTypes.value.size > 0)

  // 初始化选中状态
  const initializeSelection = () => {
    const existingKeys = Object.keys(currentValue.value)
    selectedTypes.value = new Set(existingKeys)
  }

  // 选择资质类型（单选模式）
  const selectType = (typeCode: string) => {
    if (mode === QualificationSelectionMode.SINGLE) {
      // 单选模式：清除之前的选择，设置新选择
      const previousType = selectedType.value
      selectedTypes.value.clear()
      selectedTypes.value.add(typeCode)

      // 处理数据迁移
      if (previousType && previousType !== typeCode) {
        handleTypeSwitching(previousType, typeCode)
      } else if (!currentValue.value[typeCode]) {
        // 新选择的类型，创建默认数据
        createDefaultQualificationItem(typeCode)
      }
    }
  }

  // 切换资质类型选择（多选模式）
  const toggleType = (typeCode: string) => {
    if (mode === QualificationSelectionMode.MULTIPLE) {
      if (selectedTypes.value.has(typeCode)) {
        selectedTypes.value.delete(typeCode)
        // 删除对应的数据
        delete currentValue.value[typeCode]
      } else {
        selectedTypes.value.add(typeCode)
        // 创建默认数据
        createDefaultQualificationItem(typeCode)
      }
    }
  }

  // 处理资质类型切换（单选模式）
  const handleTypeSwitching = (fromType: string, toType: string) => {
    const fromData = currentValue.value[fromType]
    const typeConfig = typeConfigMap.value.get(toType)

    if (fromData && typeConfig) {
      // 保留部分数据，创建新的资质项
      const newData: QualificationItem = {
        qualificationCode: toType,
        qualificationName: typeConfig.qualificationName,
        qualificationValidity: fromData.qualificationValidity || {
          qualValidityPeriod: 0,
          startTime: 0,
          endTime: 0
        },
        mediaInfoList: fromData.mediaInfoList || []
      }

      // 删除旧数据，添加新数据
      delete currentValue.value[fromType]
      currentValue.value[toType] = newData
    } else {
      // 删除旧数据，创建默认新数据
      delete currentValue.value[fromType]
      createDefaultQualificationItem(toType)
    }
  }

  // 创建默认资质项
  const createDefaultQualificationItem = (typeCode: string) => {
    const typeConfig = typeConfigMap.value.get(typeCode)
    if (typeConfig) {
      currentValue.value[typeCode] = {
        qualificationCode: typeCode,
        qualificationName: typeConfig.qualificationName,
        qualificationValidity: {
          qualValidityPeriod: 0,
          startTime: 0,
          endTime: 0
        },
        mediaInfoList: []
      }
    }
  }

  // 清除选择
  const clearSelection = () => {
    selectedTypes.value.clear()
    currentValue.value = {}
  }

  // 重置到初始状态
  const reset = () => {
    currentValue.value = { ...initialValue }
    initializeSelection()
  }

  // 获取指定类型的资质项
  const getQualificationItem = (typeCode: string): QualificationItem | null => {
    return currentValue.value[typeCode] || null
  }

  // 更新指定类型的资质项
  const updateQualificationItem = (typeCode: string, item: Partial<QualificationItem>) => {
    if (currentValue.value[typeCode]) {
      currentValue.value[typeCode] = {
        ...currentValue.value[typeCode],
        ...item
      }
    }
  }

  // 验证选择状态
  const validateSelection = (): { valid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (selectedTypes.value.size === 0) {
      errors.push('请选择资质类型')
    }

    // 验证每个选中的资质类型是否有效
    for (const typeCode of selectedTypes.value) {
      const typeConfig = typeConfigMap.value.get(typeCode)
      if (!typeConfig) {
        errors.push(`无效的资质类型: ${typeCode}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 监听选中状态变化
  watch(
    () => selectedTypes.value.size,
    () => {
      // 可以在这里添加选择变化的副作用
    }
  )

  // 初始化
  initializeSelection()

  return {
    // 响应式状态
    selectedTypes,
    currentValue,

    // 计算属性
    options,
    typeConfigMap,
    selectedType,
    selectedTypeList,
    hasSelection,

    // 方法
    selectType,
    toggleType,
    clearSelection,
    reset,
    getQualificationItem,
    updateQualificationItem,
    validateSelection,

    // 工具方法
    createDefaultQualificationItem
  }
}
