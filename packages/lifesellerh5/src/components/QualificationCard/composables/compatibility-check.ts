/**
 * 兼容性检查脚本
 * 用于验证重构后的组合式函数与原有组件的兼容性
 */

import type { IQualificationElement } from '@edith/edith_get_query_qualification_config'
import { useQualificationData } from './useQualificationData'
import { useQualificationValidity } from './useQualificationValidity'
import { QualificationSelectionMode, QualificationItem, LicenseValidItem } from '../core/type'

// 模拟数据
const mockElements: IQualificationElement[] = [
  {
    qualificationConfig: {
      qualificationCode: 'FOOD_LICENSE',
      qualificationName: '食品经营许可证',
      qualificationDesc: '用于食品经营的许可证'
    }
  },
  {
    qualificationConfig: {
      qualificationCode: 'BUSINESS_LICENSE',
      qualificationName: '营业执照',
      qualificationDesc: '企业营业执照'
    }
  }
]

/**
 * 检查 AND 逻辑兼容性
 */
export function checkAndLogicCompatibility() {
  console.log('🔍 检查 AND 逻辑兼容性...')
  
  const qualificationData = useQualificationData(
    mockElements,
    {
      mode: QualificationSelectionMode.SINGLE,
      validateOnChange: false,
      autoSync: false
    }
  )

  // 测试资质类型选择
  console.log('✅ 测试资质类型选择')
  qualificationData.selectType('FOOD_LICENSE')
  
  if (qualificationData.selectedType.value !== 'FOOD_LICENSE') {
    throw new Error('❌ 资质类型选择失败')
  }
  
  // 测试数据结构兼容性
  console.log('✅ 测试数据结构兼容性')
  const currentData = qualificationData.currentData.value
  const foodLicenseData = currentData['FOOD_LICENSE']
  
  if (!foodLicenseData || 
      typeof foodLicenseData.qualificationCode !== 'string' ||
      typeof foodLicenseData.qualificationName !== 'string' ||
      !Array.isArray(foodLicenseData.mediaInfoList)) {
    throw new Error('❌ 数据结构不兼容')
  }

  // 测试类型切换（AND逻辑特有）
  console.log('✅ 测试类型切换逻辑')
  qualificationData.selectType('BUSINESS_LICENSE')
  
  if (currentData['FOOD_LICENSE'] || !currentData['BUSINESS_LICENSE']) {
    throw new Error('❌ AND逻辑类型切换失败')
  }

  console.log('✅ AND 逻辑兼容性检查通过')
}

/**
 * 检查 OR 逻辑兼容性
 */
export function checkOrLogicCompatibility() {
  console.log('🔍 检查 OR 逻辑兼容性...')
  
  const qualificationData = useQualificationData(
    mockElements,
    {
      mode: QualificationSelectionMode.MULTIPLE,
      validateOnChange: false,
      autoSync: false
    }
  )

  // 测试多选功能
  console.log('✅ 测试多选功能')
  qualificationData.toggleType('FOOD_LICENSE')
  qualificationData.toggleType('BUSINESS_LICENSE')
  
  if (qualificationData.selectedTypeList.value.length !== 2) {
    throw new Error('❌ 多选功能失败')
  }

  // 测试取消选择
  console.log('✅ 测试取消选择')
  qualificationData.toggleType('FOOD_LICENSE')
  
  if (qualificationData.selectedTypeList.value.includes('FOOD_LICENSE')) {
    throw new Error('❌ 取消选择失败')
  }

  console.log('✅ OR 逻辑兼容性检查通过')
}

/**
 * 检查有效期管理兼容性
 */
export function checkValidityCompatibility() {
  console.log('🔍 检查有效期管理兼容性...')
  
  const validityManager = useQualificationValidity()

  // 测试永久有效
  console.log('✅ 测试永久有效')
  validityManager.setPermanent()
  
  if (!validityManager.isPermanent.value || !validityManager.hasValidValidity.value) {
    throw new Error('❌ 永久有效设置失败')
  }

  // 测试期限有效
  console.log('✅ 测试期限有效')
  const futureTime = Date.now() + 365 * 24 * 60 * 60 * 1000 // 一年后
  validityManager.setNonPermanent(Date.now(), futureTime)
  
  if (!validityManager.isNonPermanent.value || !validityManager.hasValidValidity.value) {
    throw new Error('❌ 期限有效设置失败')
  }

  // 测试验证逻辑
  console.log('✅ 测试验证逻辑')
  const mockImages = [
    { name: 'test', uid: '1', size: 1000, status: 'success' as const, width: 800, height: 600, url: 'test.jpg' }
  ]
  
  const validation = validityManager.validateValidityWithImages(mockImages)
  if (!validation.valid) {
    throw new Error(`❌ 验证逻辑失败: ${validation.errors.join(', ')}`)
  }

  console.log('✅ 有效期管理兼容性检查通过')
}

/**
 * 检查数据序列化兼容性
 */
export function checkSerializationCompatibility() {
  console.log('🔍 检查数据序列化兼容性...')
  
  const qualificationData = useQualificationData(
    mockElements,
    {
      mode: QualificationSelectionMode.SINGLE,
      validateOnChange: false,
      autoSync: false
    }
  )

  // 准备测试数据
  qualificationData.selectType('FOOD_LICENSE')
  
  const mockImages = [
    { name: 'test', uid: '1', size: 1000, status: 'success' as const, width: 800, height: 600, url: 'test.jpg' }
  ]
  const mockValidity: LicenseValidItem = {
    qualValidityPeriod: 1,
    startTime: Date.now(),
    endTime: Date.now() + 365 * 24 * 60 * 60 * 1000
  }

  qualificationData.updateImages('FOOD_LICENSE', mockImages)
  qualificationData.updateValidity('FOOD_LICENSE', mockValidity)

  // 测试序列化
  console.log('✅ 测试数据序列化')
  const serialized = qualificationData.serializeData()
  
  if (!serialized['FOOD_LICENSE'] || 
      serialized['FOOD_LICENSE'].qualificationCode !== 'FOOD_LICENSE' ||
      !serialized['FOOD_LICENSE'].mediaInfoList.length ||
      !serialized['FOOD_LICENSE'].qualificationValidity) {
    throw new Error('❌ 数据序列化失败')
  }

  // 测试反序列化
  console.log('✅ 测试数据反序列化')
  qualificationData.resetAll()
  qualificationData.deserializeData(serialized)
  
  if (!qualificationData.currentData.value['FOOD_LICENSE'] ||
      !qualificationData.selectedTypeList.value.includes('FOOD_LICENSE')) {
    throw new Error('❌ 数据反序列化失败')
  }

  console.log('✅ 数据序列化兼容性检查通过')
}

/**
 * 检查事件通信兼容性
 */
export function checkEventCompatibility() {
  console.log('🔍 检查事件通信兼容性...')
  
  // 这里主要检查数据结构是否符合原有组件的期望
  const qualificationData = useQualificationData(
    mockElements,
    {
      mode: QualificationSelectionMode.SINGLE,
      validateOnChange: false,
      autoSync: false
    }
  )

  qualificationData.selectType('FOOD_LICENSE')
  
  // 检查数据格式是否符合 v-model 的期望
  const modelValue = qualificationData.currentData.value
  
  if (typeof modelValue !== 'object' || 
      !modelValue['FOOD_LICENSE'] ||
      typeof modelValue['FOOD_LICENSE'].qualificationCode !== 'string') {
    throw new Error('❌ v-model 数据格式不兼容')
  }

  console.log('✅ 事件通信兼容性检查通过')
}

/**
 * 运行所有兼容性检查
 */
export function runAllCompatibilityChecks() {
  console.log('🚀 开始运行兼容性检查...\n')
  
  try {
    checkAndLogicCompatibility()
    console.log('')
    
    checkOrLogicCompatibility()
    console.log('')
    
    checkValidityCompatibility()
    console.log('')
    
    checkSerializationCompatibility()
    console.log('')
    
    checkEventCompatibility()
    console.log('')
    
    console.log('🎉 所有兼容性检查通过！')
    console.log('✅ 重构后的组合式函数与原有组件完全兼容')
    
  } catch (error) {
    console.error('💥 兼容性检查失败:', error)
    throw error
  }
}

// 如果直接运行此文件，执行所有检查
if (typeof window === 'undefined' && typeof process !== 'undefined') {
  runAllCompatibilityChecks()
}
