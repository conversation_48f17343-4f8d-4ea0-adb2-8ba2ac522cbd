import { ref, computed, watch, nextTick } from 'vue'
import type { IQualificationElement } from '@edith/edith_get_query_qualification_config'
import { 
  QualificationItem, 
  LicenseValidItem, 
  ImageItem, 
  QualificationSelectionMode,
  QualificationDataConfig
} from '../core/type'
import { useQualificationTypeSelection } from './useQualificationTypeSelection'
import { useQualificationValidity } from './useQualificationValidity'

/**
 * 资质数据统一管理组合式函数
 * 整合资质类型、图片、有效期的数据管理
 */
export function useQualificationData(
  elements: IQualificationElement[],
  config: QualificationDataConfig,
  initialValue: Record<string, QualificationItem> = {}
) {
  const {
    mode,
    validateOnChange = true,
    autoSync = true
  } = config

  // 使用资质类型选择组合式函数
  const typeSelection = useQualificationTypeSelection(elements, mode, initialValue)

  // 响应式状态
  const isLoading = ref(false)
  const globalErrors = ref<string[]>([])
  const isDirty = ref(false)

  // 为每个资质类型创建有效期管理实例
  const validityManagers = ref<Map<string, ReturnType<typeof useQualificationValidity>>>(new Map())

  // 计算属性 - 当前数据
  const currentData = computed(() => typeSelection.currentValue.value)

  // 计算属性 - 是否有数据
  const hasData = computed(() => Object.keys(currentData.value).length > 0)

  // 计算属性 - 数据完整性检查
  const dataCompleteness = computed(() => {
    const results: Record<string, { complete: boolean; missing: string[] }> = {}
    
    Object.entries(currentData.value).forEach(([typeCode, item]) => {
      const missing: string[] = []
      
      if (!item.qualificationCode) missing.push('资质类型')
      if (!item.mediaInfoList || item.mediaInfoList.length === 0) missing.push('资质图片')
      if (!item.qualificationValidity) missing.push('有效期')
      
      results[typeCode] = {
        complete: missing.length === 0,
        missing
      }
    })
    
    return results
  })

  // 计算属性 - 整体数据是否完整
  const isDataComplete = computed(() => {
    return Object.values(dataCompleteness.value).every(item => item.complete)
  })

  // 获取或创建有效期管理器
  const getValidityManager = (typeCode: string) => {
    if (!validityManagers.value.has(typeCode)) {
      const initialValidity = currentData.value[typeCode]?.qualificationValidity
      const manager = useQualificationValidity(initialValidity, {
        validateOnChange,
        requireValidityWhenHasImages: true,
        requireImagesWhenHasValidity: mode === QualificationSelectionMode.SINGLE
      })
      validityManagers.value.set(typeCode, manager)
    }
    return validityManagers.value.get(typeCode)!
  }

  // 更新图片数据
  const updateImages = (typeCode: string, images: ImageItem[]) => {
    if (!currentData.value[typeCode]) return

    currentData.value[typeCode] = {
      ...currentData.value[typeCode],
      mediaInfoList: images
    }

    // 触发有效期验证（如果有图片需要验证有效期）
    const validityManager = getValidityManager(typeCode)
    if (validateOnChange) {
      validityManager.validateValidityWithImages(images)
    }

    markDirty()
    if (autoSync) {
      syncData()
    }
  }

  // 更新有效期数据
  const updateValidity = (typeCode: string, validity: LicenseValidItem | null) => {
    if (!currentData.value[typeCode]) return

    currentData.value[typeCode] = {
      ...currentData.value[typeCode],
      qualificationValidity: validity || {
        qualValidityPeriod: 0,
        startTime: 0,
        endTime: 0
      }
    }

    // 更新有效期管理器
    const validityManager = getValidityManager(typeCode)
    validityManager.updateValidity(validity)

    markDirty()
    if (autoSync) {
      syncData()
    }
  }

  // 添加资质项
  const addQualificationItem = (typeCode: string) => {
    typeSelection.createDefaultQualificationItem(typeCode)
    
    // 创建对应的有效期管理器
    getValidityManager(typeCode)
    
    markDirty()
    if (autoSync) {
      syncData()
    }
  }

  // 删除资质项
  const removeQualificationItem = (typeCode: string) => {
    delete currentData.value[typeCode]
    validityManagers.value.delete(typeCode)
    
    markDirty()
    if (autoSync) {
      syncData()
    }
  }

  // 验证所有数据
  const validateAll = async (): Promise<{ valid: boolean; errors: Record<string, string[]> }> => {
    isLoading.value = true
    const errors: Record<string, string[]> = {}
    
    try {
      // 验证资质类型选择
      const typeValidation = typeSelection.validateSelection()
      if (!typeValidation.valid) {
        errors.global = typeValidation.errors
      }

      // 验证每个资质项的数据
      for (const [typeCode, item] of Object.entries(currentData.value)) {
        const itemErrors: string[] = []
        
        // 验证图片
        if (!item.mediaInfoList || item.mediaInfoList.length === 0) {
          itemErrors.push('请上传资质图片')
        }
        
        // 验证有效期
        const validityManager = getValidityManager(typeCode)
        const validityValidation = validityManager.validateValidityWithImages(item.mediaInfoList || [])
        if (!validityValidation.valid) {
          itemErrors.push(...validityValidation.errors)
        }
        
        if (itemErrors.length > 0) {
          errors[typeCode] = itemErrors
        }
      }

      const isValid = Object.keys(errors).length === 0
      globalErrors.value = errors.global || []
      
      return { valid: isValid, errors }
    } finally {
      isLoading.value = false
    }
  }

  // 序列化数据用于提交
  const serializeData = (): Record<string, QualificationItem> => {
    const serialized: Record<string, QualificationItem> = {}
    
    Object.entries(currentData.value).forEach(([typeCode, item]) => {
      const validityManager = getValidityManager(typeCode)
      serialized[typeCode] = {
        qualificationCode: item.qualificationCode,
        qualificationName: item.qualificationName,
        qualificationValidity: validityManager.getValidityForSubmit() || item.qualificationValidity,
        mediaInfoList: item.mediaInfoList || []
      }
    })
    
    return serialized
  }

  // 反序列化数据
  const deserializeData = (data: Record<string, QualificationItem>) => {
    typeSelection.currentValue.value = { ...data }
    
    // 重新创建有效期管理器
    validityManagers.value.clear()
    Object.entries(data).forEach(([typeCode, item]) => {
      const manager = getValidityManager(typeCode)
      manager.setValidityFromData(item.qualificationValidity)
    })
    
    // 更新选中状态
    typeSelection.selectedTypes.clear()
    Object.keys(data).forEach(typeCode => {
      typeSelection.selectedTypes.add(typeCode)
    })
    
    isDirty.value = false
  }

  // 重置所有数据
  const resetAll = () => {
    typeSelection.reset()
    validityManagers.value.clear()
    globalErrors.value = []
    isDirty.value = false
  }

  // 清除所有验证错误
  const clearAllErrors = () => {
    globalErrors.value = []
    validityManagers.value.forEach(manager => {
      manager.validationErrors.value = []
    })
  }

  // 标记数据为脏数据
  const markDirty = () => {
    isDirty.value = true
  }

  // 同步数据（可以在这里添加自动保存逻辑）
  const syncData = async () => {
    if (autoSync) {
      await nextTick()
      // 这里可以添加自动保存到服务器的逻辑
    }
  }

  // 监听数据变化
  watch(
    () => currentData.value,
    () => {
      if (validateOnChange) {
        // 延迟验证，避免频繁触发
        setTimeout(() => {
          validateAll()
        }, 300)
      }
    },
    { deep: true }
  )

  return {
    // 响应式状态
    currentData,
    isLoading,
    globalErrors,
    isDirty,
    
    // 计算属性
    hasData,
    dataCompleteness,
    isDataComplete,
    
    // 资质类型选择相关
    ...typeSelection,
    
    // 方法
    getValidityManager,
    updateImages,
    updateValidity,
    addQualificationItem,
    removeQualificationItem,
    validateAll,
    serializeData,
    deserializeData,
    resetAll,
    clearAllErrors,
    markDirty,
    syncData
  }
}
