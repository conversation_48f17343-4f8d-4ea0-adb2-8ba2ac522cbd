import { UploadStatus, DFile } from '@xhs/delight/components/Upload/upload.type'

export interface UploadFile {
  name: string
  percent?: number
  status: UploadStatus
  size: number
  response?: unknown
  uid: string
  url?: string
  base64?: string
  raw?: DFile
  error?: string
  width?: number
  height?: number
  needCrop?: boolean
  [key:string]: any
}



export interface ImageItem {
  name: string
  uid: string
  size: number
  status: UploadFile['status']
  width: number
  height: number
  url: string
  path?: string
}

// 1 非永久 0 永久
export enum Period {
  PERMANENT = 0,
  NON_PERMANENT = 1
}

export interface LicenseValidItem {
  qualValidityPeriod: Period
  startTime: number
  endTime: number
}

export interface QualificationItem {
  qualificationCode: string
  qualificationName: string
  qualificationValidity: LicenseValidItem
  mediaInfoList: ImageItem[]
}

export enum Func {
  AND = 0,
  OR = 1
}

// 资质类型选择模式
export enum QualificationSelectionMode {
  SINGLE = 'single',    // 单选模式（AND逻辑）
  MULTIPLE = 'multiple' // 多选模式（OR逻辑）
}

// 资质类型选择配置
export interface QualificationTypeConfig {
  qualificationCode: string
  qualificationName: string
  required?: boolean
  disabled?: boolean
}

// 资质数据管理配置
export interface QualificationDataConfig {
  mode: QualificationSelectionMode
  validateOnChange?: boolean
  autoSync?: boolean
}
