import { ImageItem } from '~/types/media'

// 1 非永久 0 永久
export enum Period {
  PERMANENT = 0,
  NON_PERMANENT = 1
}

export interface LicenseValidItem {
  qualValidityPeriod: Period
  startTime: number
  endTime: number
}

export interface QualificationItem {
  qualificationCode: string
  qualificationName: string
  qualificationValidity: LicenseValidItem
  mediaInfoList: ImageItem[]
}

export enum Func {
  AND = 0,
  OR = 1
}
