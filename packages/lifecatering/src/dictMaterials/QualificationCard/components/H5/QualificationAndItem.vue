<template>
  <div class="gray-card">
    <!-- 资质类型 -->
    <FormItem label="资质类型" required>
      <RadioGroup
        v-if="!readonly"
        :model-value="qualificationCode"
        :options="options"
        :disabled="disabled"
        @update:model-value="changeType"
      />
      <Text v-else>{{ qualificationName }}</Text>
    </FormItem>

    <template v-if="qualificationCode">
      <!-- 资质图片 -->
      <QualificationImage
        v-if="!(readonly && !value[qualificationCode]?.mediaInfoList?.length)"
        :name="`${name}.${qualificationCode}.mediaInfoList`"
        :model-value="value[qualificationCode]?.mediaInfoList || []"
        :decorator-props="readonly ? { required: true, rules: imageValidator, description: '' } : { required: true, rules: imageValidator }"
        :component-props="{disabled: disabled || readonly}"
        @update:model-value="changeImage"
      >
        <template v-if="qualificationConfig && qualificationConfig.qualificationDesc || qualificationConfig.qualificationImageList?.length" #label>
          <DescriptionLabel :qualification-config="qualificationConfig" />
        </template>
      </QualificationImage>

      <!-- 资质有效期 -->
      <LicenseValidRange
        title="资质有效期"
        :name="`${name}.${qualificationCode}.qualificationValidity`"
        :model-value="value[qualificationCode]?.qualificationValidity || []"
        :decorator-props="{required: true, rules: licenseValidValidRange }"
        :component-props="{disabled, readonly}"
        @update:model-value="changeValidity"
      />
    </template>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { RadioGroup, FormItem2 as FormItem, Text } from '@xhs/delight'
  import { IQualificationElement } from '@edith/edith_get_query_qualification_config'

  import { ImageItem } from '~/types/media'
  import { QualificationItem, LicenseValidItem } from '../../../QualificationImage/core/type'
  import { LicenseValidRange } from '../../../LicenseValidRange'
  import { QualificationImage } from '../../../QualificationImage'
  import { validator as licenseValidValidRange } from '../../../LicenseValidRange/core/validator'
  import { validator as imageValidator } from '../../../QualificationImage/core/validator'
  import DescriptionLabel from './DescriptionLabel.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      disabled?: boolean
      readonly?: boolean
      name?: string
      elements: IQualificationElement[]
    }>(),
    {
      name: 'qualificationMap'
    }
  )

  const emit = defineEmits(['update:modelValue'])

  // 响应式状态管理
  const value = ref(props.modelValue || {})

  // 监听外部值变化
  watch(
    () => props.modelValue,
    val => {
      value.value = val || {}
    }
  )

  // 触发变更事件
  const change = () => {
    emit('update:modelValue', value.value)
  }

  // 计算属性 - 资质类型选项
  const options = computed(() => props.elements.map(item => ({
    label: item.qualificationConfig?.qualificationName,
    value: item.qualificationConfig?.qualificationCode
  })))

  // 计算属性 - 当前选中的资质类型（AND逻辑：单选）
  const qualificationCode = computed(() => {
    const keys = props.elements.map(t => t.qualificationConfig?.qualificationCode as string)
    return keys.find(key => key in value.value) || keys[0]
  })

  // 计算属性 - 当前资质配置
  const qualificationConfig = computed(() => {
    const element = props.elements.find(e => e.qualificationConfig?.qualificationCode === qualificationCode.value)
    return element?.qualificationConfig || {}
  })

  // 计算属性 - 资质名称
  const qualificationName = computed(() => qualificationConfig.value?.qualificationName || '')

  // 修改资质类型（AND逻辑：单选互斥）
  const changeType = (val: string) => {
    if (!val) return

    // 保存当前选中资质的数据（用于数据迁移）
    const currentData = qualificationCode.value ? value.value[qualificationCode.value] : {} as QualificationItem

    // 清除旧的资质数据（单选互斥）
    if (qualificationCode.value && qualificationCode.value !== val) {
      delete value.value[qualificationCode.value]
    }

    // 查找新资质的配置信息
    const newQualificationConfig = props.elements.find(e => e.qualificationConfig?.qualificationCode === val)?.qualificationConfig

    // 创建新的资质数据（保留部分旧数据）
    const newData: QualificationItem = {
      qualificationCode: val,
      qualificationName: newQualificationConfig?.qualificationName || '',
      // 保留图片和有效期数据（如果存在）
      mediaInfoList: currentData.mediaInfoList || [],
      qualificationValidity: currentData.qualificationValidity || {
        qualValidityPeriod: 0,
        startTime: 0,
        endTime: 0
      }
    }

    // 更新数据
    value.value = {
      ...value.value,
      [val]: newData
    }

    change()
  }

  // 修改图片
  const changeImage = (val: ImageItem[]) => {
    if (!qualificationCode.value) return

    // 确保资质项存在
    if (!value.value[qualificationCode.value]) {
      value.value[qualificationCode.value] = {
        qualificationCode: qualificationCode.value,
        qualificationName: qualificationName.value,
        mediaInfoList: [],
        qualificationValidity: {
          qualValidityPeriod: 0,
          startTime: 0,
          endTime: 0
        }
      }
    }

    // 更新图片数据
    value.value[qualificationCode.value] = {
      ...value.value[qualificationCode.value],
      mediaInfoList: val
    }

    change()
  }

  // 修改有效期
  const changeValidity = (val: LicenseValidItem) => {
    if (!qualificationCode.value) return

    // 确保资质项存在
    if (!value.value[qualificationCode.value]) {
      value.value[qualificationCode.value] = {
        qualificationCode: qualificationCode.value,
        qualificationName: qualificationName.value,
        mediaInfoList: [],
        qualificationValidity: {
          qualValidityPeriod: 0,
          startTime: 0,
          endTime: 0
        }
      }
    }

    // 更新有效期数据
    value.value[qualificationCode.value] = {
      ...value.value[qualificationCode.value],
      qualificationValidity: val
    }

    change()
  }

</script>
