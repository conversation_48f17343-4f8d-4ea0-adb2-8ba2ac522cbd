<template>
  <Skeleton :loading="loading" class="qualification-outer" :class="{'gray-card': loading}">
    <template v-for="(group, key) in qualificationGroupList" :key="key">
      <component
        :is="group.func === Func.AND ? QualificationAndItem : QualificationOrItem"
        v-if="group.qualificationElements?.length"
        v-model="value"
        :readonly="componentProps?.readonly"
        :elements="group.qualificationElements || []"
      />
    </template>
  </Skeleton>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { toast2 as toast } from '@xhs/delight'
  import { getQueryQualificationConfig, IQualificationGroupList } from '@edith/edith_get_query_qualification_config'
  import Skeleton from 'shared/components/Skeleton/index.vue'
  import { QualificationItem, Func } from '../../../QualificationImage/core/type'
  import QualificationOrItem from './QualificationOrItem.vue'
  import QualificationAndItem from './QualificationAndItem.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      componentProps?: {
        categoryId?: string
        disabled?: boolean
        readonly?: boolean
        onLoad?:(loaded: boolean) => void
      }
      name?: string
    }>(),
    {
      name: 'qualificationMap'
    }
  )

  const emit = defineEmits(['update:modelValue'])

  const value = computed({
    get: () => props.modelValue,
    set: val => {
      emit('update:modelValue', val)
    }
  })

  const loading = ref(true)

  const qualificationGroupList = ref<IQualificationGroupList[]>([])

  const fetchQualificationConfig = async () => {
    const categoryId = props.componentProps?.categoryId

    if (!categoryId) return
    try {
      const res = await getQueryQualificationConfig({
        categoryId
      })
      qualificationGroupList.value = res?.qualificationGroupList || []
      if (!qualificationGroupList.value.length) {
        return toast.warning('获取资质配置失败')
      }
      // 同步获取资质配置成功后，触发load事件
      props.componentProps?.onLoad?.(true)

      // 过滤不在配置中的资质
      const modelValue = { ...props.modelValue }
      const saveKeys = Object.keys(modelValue)
      if (saveKeys.length) {
        const codes:string[] = []
        qualificationGroupList.value.forEach(t => {
          t.qualificationElements?.forEach(e => {
            const code = e.qualificationConfig?.qualificationCode
            if (code) codes.push(code)
          })
        })
        if (saveKeys.some(m => !codes.includes(m))) {
          saveKeys.forEach(m => {
            if (!codes.includes(m)) delete modelValue[m]
          })
          value.value = modelValue
        }
      }

      // 错误就不关闭loading，防止用户操作
      loading.value = false
    } catch (error: any) {
      throw new Error(`报错位置：packages/lifecatering/src/dictMaterials/QualificationCard/components/PC/QualificationCard.vue-----fetchQualificationConfig函数内调用getQueryQualificationConfig；报错信息：${error?.message || error}`)
    }
  }

  watch(
    () => props.componentProps?.categoryId,
    fetchQualificationConfig,
    { immediate: true }
  )

</script>

<style lang="stylus" scoped>
.qualification-outer
  display flex
  flex-direction column
  gap 20px
  padding-bottom 10px

.gray-card
  background-color #fafafa
  padding 20px
  border-radius 4px
  display flex
  flex-direction column
  gap: 12px
</style>
